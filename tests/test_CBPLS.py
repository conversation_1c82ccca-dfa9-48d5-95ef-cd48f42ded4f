# from data.random_data_generator import random_data
# from src.Clustering.CB_PLS import CB_PLS as cpls
# import numpy as np
# import pytest
# pytest.skip("reason", allow_module_level=True)
# N = 30
# xvar = 6
# yvar = 3
# Ntes = 1
# num_clusters = 3

assert True
# X, Y, X_test, Y_test = random_data(N=N, xvar=xvar, yvar=yvar, Ntest=Ntes)

# # FIXME: the pls evaluator outcome has changed and cause this one to error


# @pytest.mark.skip(reason="not working now")
# def test_X_clustering():

#     ClusteringMode = 2
#     cluster_pls = cpls()
#     cluster_pls.Train(X, Y, clustering_mode=ClusteringMode,
#                       K=num_clusters, ploting_clusters=True, Nrepeat=5)

#     Y_new_pre, pre_accuracy = cluster_pls.evaluator(X_test, None, Y_test)

#     print(f'prediction Accuracy (AVERAGE={np.mean(pre_accuracy)})')
#     print(f'prediction Accuracy (All={pre_accuracy})')
#     assert True


# @pytest.mark.skip(reason="not working now")
# def test_Y_clustering():

#     ClusteringMode = 3
#     cluster_pls = cpls()
#     cluster_pls.Train(X, Y, clustering_mode=ClusteringMode,
#                       K=num_clusters, ploting_clusters=True, Nrepeat=5)

#     Y_new_pre, pre_accuracy = cluster_pls.evaluator(X_test, None, Y_test)

#     print(f'prediction Accuracy (AVERAGE={np.mean(pre_accuracy)})')
#     print(f'prediction Accuracy (All={pre_accuracy})')

# assert True
