"""
Pytest-based comparison tests for MultiBlockPLS implementation

This module contains comprehensive tests comparing our MultiBlockPLS implementation
with the external mbpls.mbpls.MBPLS library using pytest framework.

Run with: pytest LVM/test_mbpls_comparison.py -v
"""

from src.LVM.multiblock_pls import MultiBlockPLS, MBPLSeval
import numpy as np
import pytest
from typing import List, Tuple, Optional


# Try to import external MBPLS
try:
    from mbpls.mbpls import MBPLS
    EXTERNAL_MBPLS_AVAILABLE = True
except ImportError:
    EXTERNAL_MBPLS_AVAILABLE = False
    MBPLS = None


class TestDataGenerator:
    """Helper class to generate consistent test data"""

    @staticmethod
    def generate_multiblock_data(seed: int = 42, n_samples: int = 80) -> <PERSON>ple[List[np.ndarray], np.ndarray]:
        """Generate synthetic multi-block data with known structure"""
        np.random.seed(seed)

        # Block dimensions
        n_features_1 = 12
        n_features_2 = 8
        n_features_3 = 6
        n_y_vars = 2

        # Generate blocks with controlled relationships
        X1 = np.random.randn(n_samples, n_features_1)
        X2 = np.random.randn(n_samples, n_features_2) + 0.3 * X1[:, :n_features_2]
        X3 = np.random.randn(n_samples, n_features_3)

        # Generate Y with known block contributions
        Y1 = (0.5 * np.mean(X1[:, :4], axis=1, keepdims=True) +
              0.3 * np.mean(X2[:, :3], axis=1, keepdims=True) +
              0.2 * np.mean(X3[:, :2], axis=1, keepdims=True) +
              0.05 * np.random.randn(n_samples, 1))

        Y2 = (0.3 * np.mean(X1[:, 4:8], axis=1, keepdims=True) +
              0.4 * np.mean(X2[:, 3:6], axis=1, keepdims=True) +
              0.3 * np.mean(X3[:, 2:4], axis=1, keepdims=True) +
              0.05 * np.random.randn(n_samples, 1))

        Y = np.hstack([Y1, Y2])

        return [X1, X2, X3], Y

    @staticmethod
    def split_data(X_blocks: List[np.ndarray], Y: np.ndarray,
                   train_ratio: float = 0.75) -> Tuple[List[np.ndarray], List[np.ndarray], np.ndarray, np.ndarray]:
        """Split data into training and testing sets"""
        n_samples = Y.shape[0]
        train_size = int(n_samples * train_ratio)

        X_blocks_train = [block[:train_size] for block in X_blocks]
        X_blocks_test = [block[train_size:] for block in X_blocks]
        Y_train = Y[:train_size]
        Y_test = Y[train_size:]

        return X_blocks_train, X_blocks_test, Y_train, Y_test


@pytest.fixture
def test_data():
    """Fixture providing test data for all tests"""
    X_blocks, Y = TestDataGenerator.generate_multiblock_data()
    X_blocks_train, X_blocks_test, Y_train, Y_test = TestDataGenerator.split_data(X_blocks, Y)

    return {
        'X_blocks_train': X_blocks_train,
        'X_blocks_test': X_blocks_test,
        'Y_train': Y_train,
        'Y_test': Y_test,
        'n_components': 4
    }


@pytest.fixture
def fitted_our_model(test_data):
    """Fixture providing a fitted MultiBlockPLS model"""
    mbpls = MultiBlockPLS()
    mbpls.fit(
        test_data['X_blocks_train'],
        test_data['Y_train'],
        n_component=test_data['n_components'],
        alpha=0.95,
        to_be_scaled=True
    )
    return mbpls


@pytest.fixture
def fitted_external_model(test_data):
    """Fixture providing a fitted external MBPLS model"""
    if not EXTERNAL_MBPLS_AVAILABLE:
        pytest.skip("External MBPLS library not available")

    # Manual scaling for external model
    X_blocks_scaled = []
    scaling_params = []

    for block in test_data['X_blocks_train']:
        Cx = np.mean(block, axis=0)
        Sx = np.std(block, axis=0, ddof=1) + 1e-16
        block_scaled = (block - Cx) / Sx
        X_blocks_scaled.append(block_scaled)
        scaling_params.append((Cx, Sx))

    Cy = np.mean(test_data['Y_train'], axis=0)
    Sy = np.std(test_data['Y_train'], axis=0, ddof=1) + 1e-16
    Y_scaled = (test_data['Y_train'] - Cy) / Sy

    ext_mbpls = MBPLS(n_components=test_data['n_components'], method="NIPALS", standardize=False)
    ext_mbpls.fit(X_blocks_scaled, Y_scaled)

    return {
        'model': ext_mbpls,
        'scaling_params': scaling_params,
        'y_scaling': (Cy, Sy)
    }


class TestMultiBlockPLSBasic:
    """Basic functionality tests for MultiBlockPLS"""

    def test_model_initialization(self):
        """Test that MultiBlockPLS initializes correctly"""
        mbpls = MultiBlockPLS()

        assert mbpls.X_blocks is None
        assert mbpls.n_blocks is None
        assert mbpls.block_weights is None
        assert mbpls.block_weights_corrected is None
        assert mbpls.W_blocks is None
        assert mbpls.P_blocks is None
        assert mbpls.T_blocks is None

    def test_model_fitting(self, test_data):
        """Test that model fits without errors and sets correct attributes"""
        mbpls = MultiBlockPLS()

        # Test fitting
        result = mbpls.fit(
            test_data['X_blocks_train'],
            test_data['Y_train'],
            n_component=test_data['n_components']
        )

        # Check return value
        assert result is mbpls

        # Check basic attributes
        assert mbpls.n_blocks == 3
        assert mbpls.n_component == test_data['n_components']
        assert mbpls.block_sizes == [12, 8, 6]

        # Check matrix dimensions
        assert mbpls.block_weights.shape == (3, test_data['n_components'])
        assert mbpls.block_weights_corrected.shape == (3, test_data['n_components'])
        assert len(mbpls.W_blocks) == 3
        assert len(mbpls.P_blocks) == 3
        assert len(mbpls.T_blocks) == 3

        # Check individual block matrix dimensions
        expected_shapes = [(12, test_data['n_components']),
                           (8, test_data['n_components']), (6, test_data['n_components'])]
        for i, expected_shape in enumerate(expected_shapes):
            assert mbpls.W_blocks[i].shape == expected_shape
            assert mbpls.P_blocks[i].shape == expected_shape

        # Check super scores
        assert mbpls.T.shape == (test_data['Y_train'].shape[0], test_data['n_components'])

    def test_prediction(self, fitted_our_model, test_data):
        """Test prediction functionality"""
        Y_pred = fitted_our_model.predict(test_data['X_blocks_test'])

        # Check prediction shape
        assert Y_pred.shape == test_data['Y_test'].shape

        # Check prediction is reasonable (not NaN or infinite)
        assert np.all(np.isfinite(Y_pred))

        # Check prediction performance (should be reasonable for synthetic data)
        rmse = np.sqrt(np.mean((test_data['Y_test'] - Y_pred)**2))
        assert rmse < 2.0, f"RMSE too high: {rmse}"

    def test_evaluation(self, fitted_our_model, test_data):
        """Test evaluation functionality"""
        eval_result = fitted_our_model.evaluation(test_data['X_blocks_test'])

        # Check return type
        assert isinstance(eval_result, MBPLSeval)

        # Check evaluation results
        assert eval_result.yfit.shape == test_data['Y_test'].shape
        assert eval_result.tscore.shape == (test_data['Y_test'].shape[0], test_data['n_components'])
        assert eval_result.HT2.shape == (test_data['Y_test'].shape[0],)

        # Check block-specific results
        assert len(eval_result.t_blocks) == 3
        assert len(eval_result.spe_blocks) == 3
        assert len(eval_result.xhat_blocks) == 3

        # Check all values are finite
        assert np.all(np.isfinite(eval_result.yfit))
        assert np.all(np.isfinite(eval_result.tscore))
        assert np.all(np.isfinite(eval_result.HT2))
        assert np.all(np.isfinite(eval_result.spe_global))

    def test_block_spe_limits(self, fitted_our_model):
        """Test block SPE limits calculation"""
        spe_limits = fitted_our_model.calculate_block_spe_limits(alpha=0.95)

        assert len(spe_limits) == 3
        assert all(limit is not None for limit in spe_limits)
        assert all(limit > 0 for limit in spe_limits)
        assert all(np.isfinite(limit) for limit in spe_limits)


class TestMultiBlockPLSScaling:
    """Test scaling and unscaling functionality"""

    def test_block_scaling(self, test_data):
        """Test that block scaling works correctly"""
        mbpls = MultiBlockPLS()

        # Test scaling
        X_blocks_scaled, Y_scaled, block_scaling, y_scaling = mbpls._scale_blocks(
            test_data['X_blocks_train'], test_data['Y_train']
        )

        # Check scaled data properties
        assert len(X_blocks_scaled) == 3
        assert len(block_scaling) == 3

        # Check that scaled blocks have approximately zero mean and unit variance
        for i, block_scaled in enumerate(X_blocks_scaled):
            assert np.allclose(np.mean(block_scaled, axis=0), 0, atol=1e-10)
            assert np.allclose(np.std(block_scaled, axis=0, ddof=1), 1, atol=1e-10)

        # Check Y scaling
        assert np.allclose(np.mean(Y_scaled, axis=0), 0, atol=1e-10)
        assert np.allclose(np.std(Y_scaled, axis=0, ddof=1), 1, atol=1e-10)

    def test_scaler_unscaler_consistency(self, fitted_our_model, test_data):
        """Test that scaling and unscaling are consistent"""
        # Scale test data
        X_blocks_scaled, Y_scaled = fitted_our_model.scaler(
            test_data['X_blocks_test'], test_data['Y_test']
        )

        # Unscale the scaled data
        X_blocks_unscaled, Y_unscaled = fitted_our_model.unscaler(X_blocks_scaled, Y_scaled)

        # Check consistency
        for i, (original, unscaled) in enumerate(zip(test_data['X_blocks_test'], X_blocks_unscaled)):
            assert np.allclose(original, unscaled, rtol=1e-10)

        assert np.allclose(test_data['Y_test'], Y_unscaled, rtol=1e-10)


@pytest.mark.skipif(not EXTERNAL_MBPLS_AVAILABLE, reason="External MBPLS library not available")
class TestComparisonWithExternal:
    """Tests comparing our implementation with external MBPLS"""

    def test_prediction_similarity(self, fitted_our_model, fitted_external_model, test_data):
        """Test that predictions are similar between implementations"""
        # Our prediction
        Y_pred_our = fitted_our_model.predict(test_data['X_blocks_test'])

        # External prediction (with manual scaling)
        ext_model = fitted_external_model['model']
        scaling_params = fitted_external_model['scaling_params']
        y_scaling = fitted_external_model['y_scaling']

        X_blocks_test_scaled = []
        for i, block in enumerate(test_data['X_blocks_test']):
            Cx, Sx = scaling_params[i]
            block_scaled = (block - Cx) / Sx
            X_blocks_test_scaled.append(block_scaled)

        Y_pred_ext_scaled = ext_model.predict(X_blocks_test_scaled)
        Cy, Sy = y_scaling
        Y_pred_ext = (Y_pred_ext_scaled * Sy) + Cy

        # Compare predictions
        rmse_our = np.sqrt(np.mean((test_data['Y_test'] - Y_pred_our)**2))
        rmse_ext = np.sqrt(np.mean((test_data['Y_test'] - Y_pred_ext)**2))

        # Predictions should be reasonably similar
        rmse_diff = abs(rmse_our - rmse_ext)
        assert rmse_diff < 0.1, f"RMSE difference too large: {rmse_diff} (Our: {rmse_our}, Ext: {rmse_ext})"

        # Correlation between predictions should be high
        correlation = np.corrcoef(Y_pred_our.flatten(), Y_pred_ext.flatten())[0, 1]
        assert correlation > 0.95, f"Prediction correlation too low: {correlation}"

    def test_block_weights_similarity(self, fitted_our_model, fitted_external_model):
        """Test that block weights are similar between implementations"""
        our_A = fitted_our_model.block_weights
        ext_A = fitted_external_model['model'].A_

        # Check shapes match
        assert our_A.shape == ext_A.shape

        # Check weights are reasonably similar
        mean_abs_diff = np.mean(np.abs(our_A - ext_A))
        assert mean_abs_diff < 0.5, f"Block weights differ too much: {mean_abs_diff}"

        # Check correlation
        correlation = np.corrcoef(our_A.flatten(), ext_A.flatten())[0, 1]
        assert correlation > 0.8, f"Block weights correlation too low: {correlation}"

    def test_corrected_weights_similarity(self, fitted_our_model, fitted_external_model):
        """Test that corrected block weights are similar"""
        our_A_corr = fitted_our_model.block_weights_corrected
        ext_A_corr = fitted_external_model['model'].A_corrected_

        # Check shapes match
        assert our_A_corr.shape == ext_A_corr.shape

        # Check corrected weights are reasonably similar
        mean_abs_diff = np.mean(np.abs(our_A_corr - ext_A_corr))
        assert mean_abs_diff < 0.5, f"Corrected block weights differ too much: {mean_abs_diff}"

    def test_super_scores_similarity(self, fitted_our_model, fitted_external_model, test_data):
        """Test that super scores are similar"""
        # Get our super scores from evaluation
        eval_result = fitted_our_model.evaluation(test_data['X_blocks_test'])
        our_ts = eval_result.tscore

        # Get external super scores (need to transform test data)
        ext_model = fitted_external_model['model']
        scaling_params = fitted_external_model['scaling_params']

        X_blocks_test_scaled = []
        for i, block in enumerate(test_data['X_blocks_test']):
            Cx, Sx = scaling_params[i]
            block_scaled = (block - Cx) / Sx
            X_blocks_test_scaled.append(block_scaled)

        ext_ts = ext_model.transform(X_blocks_test_scaled)

        # Check shapes match
        assert our_ts.shape == ext_ts.shape

        # Check correlation (scores might have different signs)
        correlations = []
        for i in range(our_ts.shape[1]):
            corr = abs(np.corrcoef(our_ts[:, i], ext_ts[:, i])[0, 1])
            correlations.append(corr)

        mean_correlation = np.mean(correlations)
        assert mean_correlation > 0.8, f"Super scores correlation too low: {mean_correlation}"


class TestEdgeCases:
    """Test edge cases and error conditions"""

    def test_empty_blocks(self):
        """Test behavior with empty blocks"""
        mbpls = MultiBlockPLS()

        with pytest.raises((ValueError, IndexError)):
            mbpls.fit([], np.array([[1, 2]]), n_component=1)

    def test_mismatched_samples(self):
        """Test behavior with mismatched number of samples"""
        mbpls = MultiBlockPLS()

        X_blocks = [np.random.rand(10, 5), np.random.rand(8, 3)]  # Different n_samples
        Y = np.random.rand(10, 2)

        with pytest.raises((ValueError, IndexError)):
            mbpls.fit(X_blocks, Y, n_component=2)

    def test_too_many_components(self, test_data):
        """Test behavior when requesting too many components"""
        mbpls = MultiBlockPLS()

        # Request more components than features
        total_features = sum(block.shape[1] for block in test_data['X_blocks_train'])

        # Should automatically limit to reasonable number
        mbpls.fit(
            test_data['X_blocks_train'],
            test_data['Y_train'],
            n_component=total_features + 10
        )

        assert mbpls.n_component <= total_features
        assert mbpls.n_component <= test_data['Y_train'].shape[0] - 1

    def test_single_component(self, test_data):
        """Test with single component"""
        mbpls = MultiBlockPLS()
        mbpls.fit(test_data['X_blocks_train'], test_data['Y_train'], n_component=1)

        assert mbpls.n_component == 1
        assert mbpls.block_weights.shape == (3, 1)

        # Test prediction still works
        Y_pred = mbpls.predict(test_data['X_blocks_test'])
        assert Y_pred.shape == test_data['Y_test'].shape


def test_performance_benchmark(test_data):
    """Benchmark test to ensure reasonable performance"""
    import time

    mbpls = MultiBlockPLS()

    start_time = time.time()
    mbpls.fit(test_data['X_blocks_train'], test_data['Y_train'], n_component=4)
    fit_time = time.time() - start_time

    start_time = time.time()
    Y_pred = mbpls.predict(test_data['X_blocks_test'])
    predict_time = time.time() - start_time

    start_time = time.time()
    eval_result = mbpls.evaluation(test_data['X_blocks_test'])
    eval_time = time.time() - start_time

    # Performance assertions (adjust thresholds as needed)
    assert fit_time < 5.0, f"Fitting too slow: {fit_time} seconds"
    assert predict_time < 1.0, f"Prediction too slow: {predict_time} seconds"
    assert eval_time < 1.0, f"Evaluation too slow: {eval_time} seconds"


if __name__ == "__main__":
    # Run tests if script is executed directly
    pytest.main([__file__, "-v", "--tb=short"])
