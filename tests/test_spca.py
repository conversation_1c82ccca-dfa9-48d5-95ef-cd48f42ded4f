import numpy as np
import pytest
from matplotlib import pyplot as plt
from src.LVM.spca import spca  # Replace 'your_module' with the actual module name


def test_spca_basic():
    """Test SPCA with a simple dataset."""
    data = np.random.rand(5, 3)
    newColOrder = spca(data, plotting=False)

    # Ensure all columns are considered
    assert len(newColOrder) == data.shape[1]
    assert np.all(np.sort(newColOrder) == np.arange(data.shape[1]))  # Ensure all columns are uniquely sorted


def test_spca_random_data():
    """Test SPCA with a random dataset."""
    np.random.seed(42)
    data = np.random.rand(10, 5)  # 10 samples, 5 features
    newColOrder = spca(data, plotting=False)

    assert len(set(newColOrder)) == 5  # Ensure all columns are sorted uniquely
    assert np.all(np.sort(newColOrder) == np.arange(data.shape[1]))  # Ensure all columns are uniquely sorted


def test_spca_plotting():
    """Test SPCA with plotting enabled (ensures no crash)."""
    data = np.random.rand(10, 3)
    newColOrder = spca(data, plotting=True)
    plt.close('all')  # Close plots after the test


def test_spca_sugg_order():
    """Test SPCA with a suggested order."""
    np.random.seed(42)
    data = np.random.rand(10, 5)  # 10 samples, 5 features
    sugg_order = np.array([4, 3, 2, 1, 0])  # Reverse order
    newColOrder = spca(data, plotting=False, sugg_order=sugg_order)

    # Ensure the suggested order is respected
    assert np.array_equal(newColOrder, sugg_order)


def test_spca_sugg_order_partial():
    """Test SPCA with a partial suggested order."""
    np.random.seed(42)
    data = np.random.rand(10, 5)  # 10 samples, 5 features
    sugg_order = np.array([4, 3])  # Partial order
    newColOrder = spca(data, plotting=False, sugg_order=sugg_order)

    # Ensure the first two columns are as suggested
    assert newColOrder[0] == 4
    assert newColOrder[1] == 3


def test_spca_sugg_order_single():
    """Test SPCA with a single suggested order."""
    np.random.seed(42)
    data = np.random.rand(10, 5)  # 10 samples, 5 features
    sugg_order = np.array([2])  # Single order

    newColOrder = spca(data, plotting=False, sugg_order=sugg_order)
    assert newColOrder[0] == 2


def test_spca_sugg_order_plotting():
    """Test SPCA with a suggested order and plotting enabled."""
    np.random.seed(42)
    data = np.random.rand(10, 5)  # 10 samples, 5 features
    sugg_order = np.array([4, 3, 2, 1, 0])  # Reverse order
    newColOrder = spca(data, plotting=True, sugg_order=sugg_order)
    plt.close('all')  # Close plots after the test

    # Ensure the suggested order is respected
    assert np.array_equal(newColOrder, sugg_order)
