import numpy as np


def clossness_metric(actual_val, Predicted_val, range_normalizer=None):
    '''
    it receives actual  and predicted and calculte the single prediction accuracy (or closeness)
    it need Y (the entire Y block to make sure there is not bias caused by the magnitude of th ecolomns)
    '''
    if range_normalizer is None:
        range_normalizer = actual_val
    pa = np.zeros_like(actual_val)
    for i in range(actual_val.shape[1]):
        base_value = np.min(range_normalizer[:, i])
        scaled_Y = range_normalizer[:, i]-base_value
        Y_avr = np.mean(scaled_Y)
        error = np.abs(actual_val[:, i]-Predicted_val[:, i])
        pa[:, i] = 1-(error/Y_avr)
    Prediction_accuracy = np.mean(pa, axis=1)
    return Prediction_accuracy


def pre_accuracy_prep(target: np.ndarray, tried: np.ndarray, Y_org: np.ndarray = None, RorMse=1):
    '''Prediction Accuracy analyzer Based on:
        R : the higher the better 
        MSE_like : The lower the better '''

    Y_org = target if Y_org is None else Y_org

    DataRange = (np.maximum(np.max(target, axis=0), np.percentile(Y_org, 95, axis=0))) - \
        (np.minimum(np.percentile(target, 5, axis=0), np.min(Y_org, axis=0)))
    diff = np.abs(target-tried)

    if RorMse == 1:
        pre_accuracy = 1-np.max(diff/DataRange, axis=1)
    elif RorMse == 2:
        pre_accuracy = np.max(diff/DataRange, axis=1)
    elif RorMse == 3:  # One Option from the past
        c = np.mean(Y_org)
        s = np.std(Y_org, ddof=1)
        Yact_scaled = (target-c) / s
        Ypre_caled = (tried-c) / s

        diff = np.sum(np.abs(Yact_scaled-Ypre_caled), axis=1)
        pre_accuracy = 1-diff
    elif RorMse == 4:  # bing the one in the manuscript
        pass
    return pre_accuracy


def rowwise_normalized_mae(predicted: np.ndarray, observed: np.ndarray, Y_org: np.ndarray):
    Y_range = np.ptp(Y_org, axis=0)
    Y_range[Y_range == 0] = 1e-8

    errors = np.abs(predicted - observed)
    errors_norm = errors / Y_range
    nmae_per_row = np.mean(errors_norm, axis=1)

    return nmae_per_row, np.mean(nmae_per_row)
