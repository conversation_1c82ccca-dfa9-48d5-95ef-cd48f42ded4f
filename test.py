from src.LVM.pls import PlsClass as mypls,plseval
from src.LVM.pca import PcaClass as pca,pcaeval
import numpy as np 
import numpy as np
from mbpls.mbpls import MBPLS
from src.LVM.pls import PlsClass as pls, plseval
num_samples = 40
num_features_x1 = 10
num_features_x2 = 5

# Generate two random data matrices X1 and X2 (two blocks)
x1 = np.random.rand(num_samples, num_features_x1)
x2 = np.random.rand(num_samples, num_features_x2)

# Generate random reference vector y
y = np.random.rand(num_samples, 2)

# Establish prediction model using 3 latent variables (components)
mbpls = MBPLS(n_components=3)
mbpls.fit([x1,x2],y)
# y_pred = mbpls.predict([x1, x2])
pls_model=pls().fit(x1,y,n_component=3,to_be_scaled=True)

pls_model.visual_plot()
pls_model