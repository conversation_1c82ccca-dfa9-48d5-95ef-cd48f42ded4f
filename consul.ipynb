{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "# from data.random_data_generator import random_data\n", "from src.LVM.pls import PlsClass as mypls,plseval\n", "from src.LVM.pca import PcaClass as pca,pcaeval\n", "import numpy as np "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from mbpls.mbpls import MBPLS\n", "from src.LVM.pls import PlsClass as pls, plseval\n", "num_samples = 40\n", "num_features_x1 = 10\n", "num_features_x2 = 5\n", "\n", "# Generate two random data matrices X1 and X2 (two blocks)\n", "x1 = np.random.rand(num_samples, num_features_x1)\n", "x2 = np.random.rand(num_samples, num_features_x2)\n", "\n", "# Generate random reference vector y\n", "y = np.random.rand(num_samples, 2)\n", "\n", "# Establish prediction model using 3 latent variables (components)\n", "mbpls = MBPLS(n_components=3)\n", "mbpls.fit([x1,x2],y)\n", "# y_pred = mbpls.predict([x1, x2])\n", "pls_model=pls().fit(x1,y,n_component=3,to_be_scaled=True)\n", "pls_model"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[0.50528227 0.89975024 0.64463048]\n", " [0.49471773 0.10024976 0.35536952]]\n", "[[0.33804527 0.81776909 0.47561235]\n", " [0.66195473 0.18223091 0.52438765]]\n"]}], "source": ["# T_score=mbpls.transform([x1, x2])\n", "# print(T_score)\n", "# print(mbpls.Ts_)\n", "print(mbpls.A_)\n", "print(mbpls.A_corrected_)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(40, 3)\n"]}], "source": ["t_k=[x @ w for x, w in zip([x1, x2], mbpls.W_)]\n", "print(t_k[1].shape)\n", "print(mbpls.W_[1].shape)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["test_size=1\n", "x1_test = np.random.rand(test_size, num_features_x1)\n", "x2_test = np.random.rand(test_size, num_features_x2)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[-0.27614223  0.25125459  0.45721413]]\n", "[[-0.08872104  0.06950825  0.06879647]]\n", "[[ 1.07984653 -0.37314977  0.02838589]]\n"]}], "source": ["X_test=[x1_test,x2_test]\n", "t_k = [x @ w for x, w in zip(X_test, mbpls.W_)]\n", "ts = np.zeros_like(t_k[0])\n", "\n", "for i in range(mbpls.A_.shape[0]):\n", "    ts += mbpls.A_corrected_[i, :]*t_k[i]\n", "print(ts)\n", "T_score=np.concatenate(X_test,axis=1)@mbpls.R_\n", "print(T_score)\n", "\n", "t_pls=pls_model.evaluation(x1_test).tscore\n", "print(t_pls)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.LVM.pls import PlsClass as pls, plseval\n", "import numpy as np\n", "num_samples = 40\n", "num_features_x1 = 10\n", "num_features_x2 = 5\n", "\n", "# Generate two random data matrices X1 and X2 (two blocks)\n", "x1 = np.random.rand(num_samples, num_features_x1)\n", "x2 = np.random.rand(num_samples, num_features_x2)\n", "\n", "# Generate random reference vector y\n", "y = np.random.rand(num_samples, 2)\n", "\n", "pls_model=pls().fit(x1,y,n_component=3)\n", "\n", "pls_model.visual_plot\n", "pls_model"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ts of my method = [[-0.13447193 -0.0275592   0.04489703]], and ts of mbpls = [[-0.02615474  0.00132636 -0.00621864]] \n", "y_pre of my method = [[0.43833473 0.46167255]], and Y_pre of mbpls = [[0.52120763 0.49242698]], direct prediction = [[-0.08216481 -0.04169493]] \n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/homebrew/lib/python3.10/site-packages/sklearn/utils/deprecation.py:151: FutureWarning: 'force_all_finite' was renamed to 'ensure_all_finite' in 1.6 and will be removed in 1.8.\n", "  warnings.warn(\n", "/opt/homebrew/lib/python3.10/site-packages/sklearn/utils/deprecation.py:151: FutureWarning: 'force_all_finite' was renamed to 'ensure_all_finite' in 1.6 and will be removed in 1.8.\n", "  warnings.warn(\n", "/opt/homebrew/lib/python3.10/site-packages/sklearn/utils/deprecation.py:151: FutureWarning: 'force_all_finite' was renamed to 'ensure_all_finite' in 1.6 and will be removed in 1.8.\n", "  warnings.warn(\n", "/opt/homebrew/lib/python3.10/site-packages/sklearn/utils/deprecation.py:151: FutureWarning: 'force_all_finite' was renamed to 'ensure_all_finite' in 1.6 and will be removed in 1.8.\n", "  warnings.warn(\n", "/opt/homebrew/lib/python3.10/site-packages/sklearn/utils/deprecation.py:151: FutureWarning: 'force_all_finite' was renamed to 'ensure_all_finite' in 1.6 and will be removed in 1.8.\n", "  warnings.warn(\n"]}], "source": ["from src.LVM.mymbpls import MyMBPLS\n", "\n", "mymbpls_model=MyMBPLS(n_components=3,X_org=[x1,x2],Y_org=y)\n", "mymbpls_model.evaluate([x1_test,x2_test])\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["X shape: (100, 53)\n", "Y shape: (100, 1)\n"]}], "source": ["from sklearn.datasets import fetch_openml\n", "\n", "# This is the correct ID for Energy Efficiency dataset\n", "data = fetch_openml(data_id=44079, as_frame=True)\n", "\n", "df = data.frame\n", "X = df.iloc[:100, :-2].values.astype(float)  # First 8 columns are features\n", "Y = df.iloc[:100, -2:-1].values.astype(float)  # Last 2 columns are target: Heating & Cooling load\n", "\n", "print(\"X shape:\", X.shape)\n", "print(\"Y shape:\", Y.shape)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "X_train, X_validation, Y_train, Y_validation = train_test_split(\n", "    X, Y, test_size=0.3, shuffle=True, random_state=42)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:101: RuntimeWarning: divide by zero encountered in matmul\n", "  P1 = (t1.T @ X) / (t1.T @ t1)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:101: RuntimeWarning: overflow encountered in matmul\n", "  P1 = (t1.T @ X) / (t1.T @ t1)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:101: RuntimeWarning: invalid value encountered in matmul\n", "  P1 = (t1.T @ X) / (t1.T @ t1)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:103: RuntimeWarning: divide by zero encountered in matmul\n", "  t_new = ((P1 @ X.T) / (P1.T @ P1)).T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:103: RuntimeWarning: overflow encountered in matmul\n", "  t_new = ((P1 @ X.T) / (P1.T @ P1)).T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:103: RuntimeWarning: invalid value encountered in matmul\n", "  t_new = ((P1 @ X.T) / (P1.T @ P1)).T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:202: RuntimeWarning: divide by zero encountered in matmul\n", "  X_hat = score @ loading.T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:202: RuntimeWarning: overflow encountered in matmul\n", "  X_hat = score @ loading.T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:202: RuntimeWarning: invalid value encountered in matmul\n", "  X_hat = score @ loading.T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:124: RuntimeWarning: divide by zero encountered in matmul\n", "  self.x_hat = ((T @ P.T) * Sx) + Cx\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:124: RuntimeWarning: overflow encountered in matmul\n", "  self.x_hat = ((T @ P.T) * Sx) + Cx\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:124: RuntimeWarning: invalid value encountered in matmul\n", "  self.x_hat = ((T @ P.T) * Sx) + Cx\n", "/opt/homebrew/lib/python3.10/site-packages/sklearn/utils/extmath.py:203: RuntimeWarning: divide by zero encountered in matmul\n", "  ret = a @ b\n", "/opt/homebrew/lib/python3.10/site-packages/sklearn/utils/extmath.py:203: RuntimeWarning: overflow encountered in matmul\n", "  ret = a @ b\n", "/opt/homebrew/lib/python3.10/site-packages/sklearn/utils/extmath.py:203: RuntimeWarning: invalid value encountered in matmul\n", "  ret = a @ b\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:101: RuntimeWarning: divide by zero encountered in matmul\n", "  P1 = (t1.T @ X) / (t1.T @ t1)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:101: RuntimeWarning: overflow encountered in matmul\n", "  P1 = (t1.T @ X) / (t1.T @ t1)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:101: RuntimeWarning: invalid value encountered in matmul\n", "  P1 = (t1.T @ X) / (t1.T @ t1)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:103: RuntimeWarning: divide by zero encountered in matmul\n", "  t_new = ((P1 @ X.T) / (P1.T @ P1)).T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:103: RuntimeWarning: overflow encountered in matmul\n", "  t_new = ((P1 @ X.T) / (P1.T @ P1)).T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:103: RuntimeWarning: invalid value encountered in matmul\n", "  t_new = ((P1 @ X.T) / (P1.T @ P1)).T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:202: RuntimeWarning: divide by zero encountered in matmul\n", "  X_hat = score @ loading.T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:202: RuntimeWarning: overflow encountered in matmul\n", "  X_hat = score @ loading.T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:202: RuntimeWarning: invalid value encountered in matmul\n", "  X_hat = score @ loading.T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:124: RuntimeWarning: divide by zero encountered in matmul\n", "  self.x_hat = ((T @ P.T) * Sx) + Cx\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:124: RuntimeWarning: overflow encountered in matmul\n", "  self.x_hat = ((T @ P.T) * Sx) + Cx\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:124: RuntimeWarning: invalid value encountered in matmul\n", "  self.x_hat = ((T @ P.T) * Sx) + Cx\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Error (AVERAGE=0.07021551557714831)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:140: RuntimeWarning: divide by zero encountered in matmul\n", "  w = X.T @ u / (u.T @ u)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:140: RuntimeWarning: overflow encountered in matmul\n", "  w = X.T @ u / (u.T @ u)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:140: RuntimeWarning: invalid value encountered in matmul\n", "  w = X.T @ u / (u.T @ u)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:142: RuntimeWarning: divide by zero encountered in matmul\n", "  t1 = X @ w\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:142: RuntimeWarning: overflow encountered in matmul\n", "  t1 = X @ w\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:142: RuntimeWarning: invalid value encountered in matmul\n", "  t1 = X @ w\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:148: RuntimeWarning: divide by zero encountered in matmul\n", "  P1 = X.T @ t1 / (t1.T @ t1)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:148: RuntimeWarning: overflow encountered in matmul\n", "  P1 = X.T @ t1 / (t1.T @ t1)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:148: RuntimeWarning: invalid value encountered in matmul\n", "  P1 = X.T @ t1 / (t1.T @ t1)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:257: RuntimeWarning: divide by zero encountered in matmul\n", "  X_hat = score @ loading.T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:257: RuntimeWarning: overflow encountered in matmul\n", "  X_hat = score @ loading.T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:257: RuntimeWarning: invalid value encountered in matmul\n", "  X_hat = score @ loading.T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:168: RuntimeWarning: divide by zero encountered in matmul\n", "  Wstar = W @ np.linalg.pinv(P.T @ W)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:168: RuntimeWarning: overflow encountered in matmul\n", "  Wstar = W @ np.linalg.pinv(P.T @ W)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:168: RuntimeWarning: invalid value encountered in matmul\n", "  Wstar = W @ np.linalg.pinv(P.T @ W)\n", "/opt/homebrew/lib/python3.10/site-packages/numpy/linalg/_linalg.py:3437: RuntimeWarning: divide by zero encountered in matmul\n", "  return _core_matmul(x1, x2)\n", "/opt/homebrew/lib/python3.10/site-packages/numpy/linalg/_linalg.py:3437: RuntimeWarning: overflow encountered in matmul\n", "  return _core_matmul(x1, x2)\n", "/opt/homebrew/lib/python3.10/site-packages/numpy/linalg/_linalg.py:3437: RuntimeWarning: invalid value encountered in matmul\n", "  return _core_matmul(x1, x2)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:170: RuntimeWarning: divide by zero encountered in matmul\n", "  S = np.linalg.svd(T.T @ T)[1]**0.5\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:170: RuntimeWarning: overflow encountered in matmul\n", "  S = np.linalg.svd(T.T @ T)[1]**0.5\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:170: RuntimeWarning: invalid value encountered in matmul\n", "  S = np.linalg.svd(T.T @ T)[1]**0.5\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:191: RuntimeWarning: divide by zero encountered in matmul\n", "  self.x_hat_scaled = T @ P.T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:191: RuntimeWarning: overflow encountered in matmul\n", "  self.x_hat_scaled = T @ P.T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pls.py:191: RuntimeWarning: invalid value encountered in matmul\n", "  self.x_hat_scaled = T @ P.T\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: divide by zero encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: overflow encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n", "/Users/<USER>/Documents/My_Workspace/src/LVM/pca.py:195: RuntimeWarning: invalid value encountered in matmul\n", "  TT = np.linalg.inv(self.T.T @ self.T)\n"]}, {"data": {"text/plain": ["(array([0.0830673 , 0.01233443, 0.04429348, 0.01128862, 0.02301395,\n", "        0.01842499, 0.04010778, 0.20234934, 0.01745332, 0.10245124,\n", "        0.04727261, 0.121238  , 0.04118129, 0.13844057, 0.02844041,\n", "        0.15875896, 0.05493861, 0.01755509, 0.03411698, 0.05159056,\n", "        0.12684557, 0.10237699, 0.01874079, 0.16879766, 0.11434022,\n", "        0.00749096, 0.0075579 , 0.16028524, 0.05926722, 0.09244537]),\n", " np.float64(0.07021551557714831))"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from src.LVM.cpls import CPLS\n", "\n", "cpls_model=CPLS(X,Y,n_cluster=2)\n", "cpls_model.fit()\n", "cpls_model.evaluate(X_validation,Y_validation)\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 1, 0, 1, 0, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0,\n", "       0, 1, 0, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1,\n", "       1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1,\n", "       0, 1, 1, 0], dtype=int32)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["cpls_model.kmeans.labels_\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}