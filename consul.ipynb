%load_ext autoreload
%autoreload 2

# from data.random_data_generator import random_data
from src.LVM.pls import PlsClass as mypls,plseval
from src.LVM.pca import PcaClass as pca,pcaeval
import numpy as np 

import numpy as np
from mbpls.mbpls import MBPLS
from src.LVM.pls import PlsClass as pls, plseval
num_samples = 40
num_features_x1 = 10
num_features_x2 = 5

# Generate two random data matrices X1 and X2 (two blocks)
x1 = np.random.rand(num_samples, num_features_x1)
x2 = np.random.rand(num_samples, num_features_x2)

# Generate random reference vector y
y = np.random.rand(num_samples, 2)

# Establish prediction model using 3 latent variables (components)
mbpls = MBPLS(n_components=3)
mbpls.fit([x1,x2],y)
# y_pred = mbpls.predict([x1, x2])
pls_model=pls().fit(x1,y,n_component=3,to_be_scaled=True)
pls_model

# T_score=mbpls.transform([x1, x2])
# print(T_score)
# print(mbpls.Ts_)
print(mbpls.A_)
print(mbpls.A_corrected_)


t_k=[x @ w for x, w in zip([x1, x2], mbpls.W_)]
print(t_k[1].shape)
print(mbpls.W_[1].shape)


test_size=1
x1_test = np.random.rand(test_size, num_features_x1)
x2_test = np.random.rand(test_size, num_features_x2)

X_test=[x1_test,x2_test]
t_k = [x @ w for x, w in zip(X_test, mbpls.W_)]
ts = np.zeros_like(t_k[0])

for i in range(mbpls.A_.shape[0]):
    ts += mbpls.A_corrected_[i, :]*t_k[i]
print(ts)
T_score=np.concatenate(X_test,axis=1)@mbpls.R_
print(T_score)

t_pls=pls_model.evaluation(x1_test).tscore
print(t_pls)


from src.LVM.pls import PlsClass as pls, plseval
import numpy as np
num_samples = 40
num_features_x1 = 10
num_features_x2 = 5

# Generate two random data matrices X1 and X2 (two blocks)
x1 = np.random.rand(num_samples, num_features_x1)
x2 = np.random.rand(num_samples, num_features_x2)

# Generate random reference vector y
y = np.random.rand(num_samples, 2)

pls_model=pls().fit(x1,y,n_component=3)

pls_model.visual_plot
pls_model

from src.LVM.mymbpls import MyMBPLS

mymbpls_model=MyMBPLS(n_components=3,X_org=[x1,x2],Y_org=y)
mymbpls_model.evaluate([x1_test,x2_test])



from sklearn.datasets import fetch_openml

# This is the correct ID for Energy Efficiency dataset
data = fetch_openml(data_id=44079, as_frame=True)

df = data.frame
X = df.iloc[:100, :-2].values.astype(float)  # First 8 columns are features
Y = df.iloc[:100, -2:-1].values.astype(float)  # Last 2 columns are target: Heating & Cooling load

print("X shape:", X.shape)
print("Y shape:", Y.shape)


from sklearn.model_selection import train_test_split

X_train, X_validation, Y_train, Y_validation = train_test_split(
    X, Y, test_size=0.3, shuffle=True, random_state=42)

from src.LVM.cpls import CPLS

cpls_model=CPLS(X,Y,n_cluster=2)
cpls_model.fit()
cpls_model.evaluate(X_validation,Y_validation)


cpls_model.kmeans.labels_
