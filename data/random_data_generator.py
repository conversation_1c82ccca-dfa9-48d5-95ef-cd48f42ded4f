import numpy as np

# TODO: add a feature that set the random seed for when repeating the data is needed


def random_data(N, xvar, yvar, Ntest=0, scaled=False):
    # Generating data
    Num_observation = N
    Ninput = xvar
    Noutput = yvar

    X = np.random.rand(Num_observation, Ninput)
    Beta = np.random.rand(Ninput, Noutput) * 2 - 1
    Y = (X @ Beta)

    X_test = None
    Y_test = None
    if Ntest != 0:
        Num_testing = Ntest
        X_test = np.random.rand(Num_testing, Ninput)
        Y_test = (X_test @ Beta)
    if scaled:
        X, Y, X_test, Y_test = data_scaler(X, Y, X_test, Y_test)

    return X, Y, X_test, Y_test


def data_scaler(X: np.ndarray, Y: np.ndarray = None, xtes: np.ndarray = None, ytes: np.ndarray = None):
    Ys, xtess, ytess = None, None, None
    Cx = X.mean(axis=0)
    Sx = X.std(axis=0, ddof=1)
    Xs = (X-Cx)/Sx

    if Y is not None:
        Cy = Y.mean(axis=0)
        Sy = Y.std(axis=0, ddof=1)
        Ys = (Y-Cy)/Sy
    if xtes is not None:
        xtess = (xtes-Cx)/Sx
    if ytes is not None:
        ytess = (ytes - Cy)/Sy
    scaled = (Xs, Ys, xtess, ytess)
    return scaled
