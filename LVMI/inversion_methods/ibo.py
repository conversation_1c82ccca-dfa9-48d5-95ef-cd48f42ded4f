import numpy as np
from abc import ABC, abstractmethod
# from case_studies.base_case import Problem
from LVM.pls import PlsClass as pls
from LVMI.inversion_methods.inversion_main_class import InversionMainClass


class IbO(InversionMainClass):
    def __init__(self, active_case, Npls_component=None, Npca_component=None, Knn: int = None):
        super().__init__(active_case=active_case, Npls_component=Npls_component, Npca_component=Npca_component, Knn=Knn)

    def execute(self,  npls: int = None, optim_param={'nPop': 200, 'MaxIt': 100, 'print': False}, knn_visualplot=False, y_des_importance=None):
        Y_des = self.Y_des
        active_case = self.active_case
        x_trin, y_trin = self.knn_update(knn_visualplot=knn_visualplot)
        y_des_importance = self.Y_des_importance if y_des_importance is None else y_des_importance

        pls_model = pls().fit(x_trin, y_trin, n_component=npls)

        x_sugg = pls_model.Ibo(y_desired=Y_des, Xmin=self.Xmin, Xmax=self.Xmax,
                               constraints=active_case.applying_conditions_X, general_params=optim_param, L=y_des_importance)[0].reshape(1, -1)

        self.x_itr = np.vstack((self.x_itr, x_sugg))
        self.n_itr += 1

        return x_sugg
