import numpy as np
# from case_studies.base_case import Problem
from LVM.pls import PlsClass as pls
from LVMI.inversion_methods.inversion_main_class import InversionMainClass
from LVMI.util.prediction_accuracy import prediction_accuracy


class PDS_ranking_dis(InversionMainClass):
    def __init__(self, active_case, **kwargs):
        super().__init__(active_case=active_case, **kwargs)

    def execute(self, prep_model, vicinity_neighbors=3, y_des=None, prep_inclusion_in_ranking=False):
        ''' It should receive pds and rank them based on its knn neighbors from training samples accuracy toward the target 
            and the distance from the training samples        
        '''
        y_des = self.Y_des if y_des is None else y_des
        x_pds = prep_model.itr_history[-1].X_pds
        x_train_all = self.X_org
        ranking_metric = []

        for i in range(x_pds.shape[0]):
            distance = np.linalg.norm(x_pds[i] - x_train_all, axis=1)
            nearest_idx = np.argsort(distance)[:vicinity_neighbors]  # Get 3 nearest neighbors
            clossness_of_nearest = prediction_accuracy(
                target=y_des, tried=self.Y_org[nearest_idx], Y_org=self.Y_org, RorMse=2).reshape(-1, 1)
            distance_of_nearest = distance[nearest_idx].reshape(-1, 1)
            ranking_metric.append((clossness_of_nearest * distance_of_nearest).mean())

        ranking_metric = np.array(
            ranking_metric)*prep_model.itr_history[-1].pds_prep_score if prep_inclusion_in_ranking else np.array(ranking_metric)

        arg_min = np.argmin(ranking_metric)
        x_sugg = x_pds[arg_min]

        self.x_itr = np.vstack((self.x_itr, x_sugg))
        self.n_itr += 1

        return x_sugg
