import numpy as np
from abc import ABC, abstractmethod
# from case_studies.base_case import Problem
from LVM.pls import PlsClass as pls
from LVMI.inversion_methods.inversion_main_class import InversionMainClass


class DirectInversion(InversionMainClass):
    def __init__(self, active_case, Npls_component=None, Npca_component=None, Knn: int = None):
        super().__init__(active_case=active_case, Npls_component=Npls_component, Npca_component=Npca_component, Knn=Knn)

    def execute(self, npls: int = None, knn_visualplot=False):
        Y_des = self.Y_des
        x_trian, y_trian = self.knn_update(knn_visualplot=knn_visualplot)

        pls_model = pls().fit(x_trian, y_trian, n_component=npls)
        x_sugg = pls_model.x_predict(Y_des, method=1)
        x_sugg = np.clip(x_sugg, self.Xmin, self.Xmax)

        self.x_itr = np.vstack((self.x_itr, x_sugg))
        self.n_itr += 1

        return x_sugg
