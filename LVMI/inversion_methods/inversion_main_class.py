
import numpy as np
from abc import ABC, abstractmethod
# from case_studies.base_case import Problem


class InversionMainClass(ABC):
    def __init__(self, active_case, Npls_component=None, Npca_component=None, Knn: int = None):
        self.active_case = active_case
        self.X_org = active_case.X
        self.Y_org = active_case.Y
        self.Xmin = active_case.Xmin
        self.steps = active_case.steps
        self.Xmax = active_case.Xmax
        self.xvarlabel = active_case.xvarlabel
        self.yvarlabel = active_case.yvarlabel
        self.Y_des = active_case.Y_des
        self.Y_des_range = active_case.Y_des_range
        self.Y_des_importance = active_case.Y_des_importance

        self.NplsCom = self.X_org.shape[1] if Npls_component is None else Npls_component
        self.NpcaCom = self.X_org.shape[1]-1 if Npca_component is None else Npca_component
        self.Knn: int = self.NplsCom+2 if Knn is None or Knn < self.NplsCom+2 else Knn

        self.x_itr = np.empty((0, self.X_org.shape[1]))
        self.y_itr = np.empty((0, self.Y_org.shape[1]))
        self.accuracy = []
        self.n_itr = 0

    def execute(self):
        ''' To be determined by Children '''

    def knn_update(self, knn_visualplot=False):
        y_des = self.Y_des
        knn = self.Knn

        x_itr = self.x_itr
        y_itr = self.y_itr

        X_train, Y_train = self.active_case.knn_update(
            x_itr=x_itr, y_itr=y_itr, y_des=y_des, knn=knn, visualplot=knn_visualplot)
        return X_train, Y_train
