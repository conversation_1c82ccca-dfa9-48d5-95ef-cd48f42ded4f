from __future__ import annotations
import numpy as np
from LVM.pls import PlsClass as pls, plseval
from LVM.pca import PcaClass as pca, pcaeval
from LVMI.base_problem.base_case import Problem
from LVMI.util.prediction_accuracy import prediction_accuracy as prediction_accuracy
from optim.pso import Pso
import matplotlib.pyplot as plt
from matplotlib.cm import ScalarMappable
from matplotlib.colors import Normalize
import pandas as pd


class PrepModel:
    def __init__(self, active_case: Problem = Problem(),
                 Npls_component=None, Npca_component=None, what_metric=np.array(range(6)), Knn: int = None, Knn_manual=False,
                 minNsolution: int = 20, NegativeCoeff: bool = False, NumPoint_NS: int = 10, validation_inclusion=False,
                 MI: int = 1, alfa_lvm: float = 0.95, RorMse: int = 1, pds_with_NS=True,
                 optim_param=None):

        # data related
        self.active_case: Problem = active_case
        self.X_org = active_case.X
        self.Y_org = active_case.Y
        self.Xmin = active_case.Xmin
        self.steps = active_case.steps
        self.Xmax = active_case.Xmax
        self.Y_des = active_case.Y_des
        self.Y_des_range = active_case.Y_des_range
        self.Y_des_importance = active_case.Y_des_importance

        # prep model properties
        self.itr_dict_ = {0: {'x': None, 'y': None, 'accuracy': 'NA'}}
        self.x_itr = np.empty((0, self.active_case.X.shape[1]))
        self.y_itr = np.empty((0, self.active_case.Y.shape[1]))
        self.iteration_assessment = np.empty((0, 1), dtype=float)
        self.itr_history: list[PrepModel.ItreInfo] = []
        self.itr = 0

        # prep params
        self.RorMse: int = RorMse
        self.what_metric = what_metric
        self.MI: int = MI
        self.pds_with_NS = pds_with_NS
        self.validation_inclusion = validation_inclusion
        self.NplsCom = self.X_org.shape[1] if Npls_component is None else Npls_component
        self.NpcaCom = self.X_org.shape[1]-1 if Npca_component is None else Npca_component
        self.Knn: int = self.NplsCom+2 if Knn is None else Knn
        self.Knn_manual: bool = Knn_manual
        self.minNsolution: int = minNsolution
        self.NegativeCoeff: bool = NegativeCoeff
        self.alfa_lvm: float = alfa_lvm
        self.NumPoint_NS: int = NumPoint_NS

        optim_param_defauld: dict = {'MaxIt': 500, 'nPop': 500, 'print': False}
        if optim_param is not None:
            optim_param_defauld.update(optim_param)
        self.optim_param = optim_param_defauld

    def __repr__(self):
        type_id = self.active_case.case_id
        current_itr = self.itr
        itr_assess = self.iteration_assessment
        best_result = np.max(itr_assess) if itr_assess.size > 0 else 0
        return f"Case:{self.active_case}, type_id:{type_id},current itration:{current_itr}, best_results:{best_result}"

    def execute(self):
        ''' Execute PREP step by step'''
        self.itr_history.append(self.ItreInfo(prep_model=self))

        current_itr = self.itr_history[-1]

        self.Knn_update(current_itr=current_itr)

        self.apply_prep(current_itr=current_itr)

        self.pds_update(current_itr=current_itr)

        self.pds_prep_score_update(current_itr=current_itr)

    def complete_current_itr(self, y_exp_current_itr: np.ndarray, x_exp_current_itr: np.ndarray = None):

        current_itr = self.itr_history[-1]
        x_exp_current_itr = current_itr.xitr_ if x_exp_current_itr is None else x_exp_current_itr
        if y_exp_current_itr.shape[0] == x_exp_current_itr.shape[0]:
            current_itr.yitr_ = y_exp_current_itr
            current_itr.xitr_ = x_exp_current_itr
        else:
            raise NotImplementedError()

        yitr_new = current_itr.yitr_
        n_iteration = len(self.itr_history)

        accuracy = prediction_accuracy(target=current_itr.Y_des, tried=yitr_new,
                                       RorMse=self.RorMse, Y_org=self.active_case.Y)
        self.itr_dict_[n_iteration] = {'x': x_exp_current_itr, 'y': yitr_new, 'accuracy': accuracy.reshape(-1, 1)}

        self.prep_property_update()

    def Knn_update(self, current_itr: ItreInfo):
        ''' Updating or creating the local samples'''
        # receiving data
        x_itr = self.x_itr
        y_itr = self.y_itr
        y_des = self.Y_des
        knn = self.Knn
        visualplot = self.Knn_manual

        # operation
        if not self.validation_inclusion:
            current_itr.X_train, current_itr.Y_train = self.active_case.knn_update(
                x_itr=x_itr, y_itr=y_itr, y_des=y_des, knn=knn, visualplot=visualplot)
        else:
            Knn_x, Knn_y = self.active_case.knn_update(
                x_itr=x_itr, y_itr=y_itr, y_des=y_des, knn=2*knn, visualplot=visualplot)
            current_itr.X_train = Knn_x[:knn]
            current_itr.Y_train = Knn_y[:knn]
            current_itr.X_val = Knn_x[knn:]
            current_itr.Y_val = Knn_y[knn:]

    def model_alignment_matrix(self, pls_model, pca_model, x_test):

        pls_metrics: plseval = pls_model.evaluation(x_test)
        pls_alignment = np.column_stack((pls_metrics.HT2, pls_metrics.spex, pls_metrics.hpls))

        pca_metrics: pcaeval = pca_model.evaluation(x_test)
        pca_alignment = np.column_stack((pca_metrics.HT2, pca_metrics.spe, pca_metrics.hpca))

        return np.column_stack((pls_alignment, pca_alignment))

    def apply_prep(self, current_itr: ItreInfo):
        ''' calculate the PREP equation'''
        current_itr.PLS_XY = pls().fit(current_itr.X_train, current_itr.Y_train, n_component=self.NplsCom, alpha=self.alfa_lvm)
        current_itr.PCA_X = pca().fit(current_itr.X_train, n_component=self.NpcaCom, alpha=self.alfa_lvm)

        if current_itr.X_val.size > 0:
            model_alignment = self.model_alignment_matrix(current_itr.PLS_XY, current_itr.PCA_X, current_itr.X_val)
            y_fit: plseval = current_itr.PLS_XY.evaluation(xtes=current_itr.X_val).yfit
        else:
            model_alignment = current_itr.model_alignment
            y_fit = np.empty((current_itr.Y_train.shape), dtype=float)
            for i in range(current_itr.X_train.shape[0]):
                xtes = current_itr.X_train[i].reshape(1, -1)
                xtrain = np.delete(current_itr.X_train, i, axis=0)
                Ytrain = np.delete(current_itr.Y_train, i, axis=0)
                pls_model = pls().fit(X=xtrain, Y=Ytrain, n_component=self.NplsCom, alpha=self.alfa_lvm)
                pca_model = pca().fit(X=xtrain, n_component=self.NpcaCom, alpha=self.alfa_lvm)
                model_alignment[i] = self.model_alignment_matrix(pls_model, pca_model, xtes)
                y_fit[i] = pls_model.evaluation(xtes=xtes).yfit

        # scaling
        model_alignment_scal_agend = np.percentile(model_alignment, 90, axis=0)
        model_alignment_scal_agend[[0, 1, 3, 4]
                                   ] = current_itr.PLS_XY.T2_lim[-1], current_itr.PLS_XY.SPE_lim_x[-1], current_itr.PCA_X.T2_lim[-1], current_itr.PCA_X.SPE_lim_x[-1]

        # check for removal
        to_be_removed_metrics = np.where(model_alignment_scal_agend < 1e-6)[0]
        if to_be_removed_metrics.size > 0:
            model_alignment_scal_agend = np.delete(model_alignment_scal_agend, to_be_removed_metrics)
            model_alignment = np.delete(model_alignment, to_be_removed_metrics, axis=1)
            current_itr.prep_components = np.delete(current_itr.prep_components, to_be_removed_metrics)
            current_itr.what_metric = np.delete(current_itr.what_metric, to_be_removed_metrics)

        model_alignment_scalled = model_alignment/model_alignment_scal_agend

        current_itr.model_alignment = model_alignment
        current_itr.model_alignment_scalled = model_alignment_scalled
        current_itr.model_alignment_scal_agent = model_alignment_scal_agend

        # calculate PA
        y_act = current_itr.Y_val if current_itr.Y_val.size > 0 else current_itr.Y_train
        PA = prediction_accuracy(y_act, y_fit, self.Y_org, self.RorMse)
        current_itr.model_alignment_PA = PA
        # optimization
        prep_eq, prep_cost, prep_sol = self.prep_eq_optimization(current_itr=current_itr)

        current_itr.prep_eq = prep_eq
        current_itr.prep_cost = prep_cost
        current_itr.prep_sol = prep_sol

    def prep_eq_optimization(self, current_itr: ItreInfo):

        # receiving data
        mian_matrix = current_itr.model_alignment_scalled
        accuracy = current_itr.model_alignment_PA

        # optim param
        nVar = 2*mian_matrix.shape[1]
        VarMin = -1 if self.NegativeCoeff else 1e-5
        VarMax = 1

        des_slop = -1 if self.RorMse == 1 else 1
        des_intercept = 1 if self.RorMse == 1 else 0

        # Cost Fcn Param
        CostFcnParam = {
            'main_matrix': mian_matrix,
            'PA': accuracy,
            'des_slope': des_slop,
            'des_intercept': des_intercept,
        }
        my_problem = Pso.OptimProblem(CostFcn=self.prepCost, nVar=nVar, VarMin=VarMin,
                                      VarMax=VarMax, CostFcnParam=CostFcnParam)
        pso_model: Pso = Pso(problem=my_problem, pso_params={'wdamp': .99}, general_params=self.optim_param)
        current_itr.optim_model = pso_model
        return pso_model.best.Position, pso_model.best.Cost, pso_model.best.Sol

    @staticmethod
    def prepCost(Xsolution, Params):

        MainMatrix: np.ndarray = Params['main_matrix']
        ErrorScore: np.ndarray = Params['PA']
        SlopDes = Params['des_slope']
        IntercDes = Params['des_intercept']
        Params['nfe'] += 1

        Npar = MainMatrix.shape[1]

        Coeff = Xsolution[:Npar]
        Powers = Xsolution[Npar:]

        PREP = np.sum(Coeff*(MainMatrix ** Powers), axis=1)
        slope_fit, intercept_fit = None, None  # np.polyfit(PREP, ErrorScore, 1)

        IdealPREP = (ErrorScore-IntercDes)/SlopDes

        DeviateFromIdeal = np.sum(np.abs(IdealPREP-PREP))

        Cost = DeviateFromIdeal  # + np.abs(SlopDes-slope_fit) + np.abs(IntercDes-intercept_fit)
        Sol = {  # 'slop[des,fit]': [SlopDes, slope_fit], 'intercept[des,fit]': [IntercDes, intercept_fit],
            'Prep Score [ideal]': IdealPREP,
            'Prep Score [fitted]': PREP,
            'w': Coeff,
            'p': Powers,
            'accuracy': ErrorScore,
            'cost': Cost
        }

        return Cost, Sol

    def pds_update(self, current_itr: ItreInfo):
        '''update or generate the PDS corresponding the target and rank them based on
        Prep_optimi equation'''
        # receiving data
        PLS_XY: pls = current_itr.PLS_XY
        xVar, yVar = PLS_XY.Xtrain_normal.shape[1], PLS_XY.Ytrain_normal.shape[1]
        Y_des = self.Y_des
        active_case = self.active_case

        minNsolution = self.minNsolution
        NumPoint = self.NumPoint_NS
        MI_method = self.MI

        PDS_X = np.empty((0, xVar), dtype=float)
        if self.pds_with_NS:
            # operation
            # TODO: I can change the NS to t_to_Y or all_together
            for i in range(yVar):
                __, Ns_x, __ = PLS_XY.null_space_single_col_X_to_Y(
                    Y_des=Y_des, which_col=i, Num_point=NumPoint, MI_method=MI_method)
                PDS_X = np.vstack((PDS_X, Ns_x))

            PDS_Y: plseval = PLS_XY.evaluation(xtes=PDS_X).yfit

            PDS_X, PDS_Y = active_case.applying_conditions_X(x_data=PDS_X, y_data=PDS_Y)

        if PDS_X.shape[0] < minNsolution:
            '''Implement x direct optim'''
            Nneeded = minNsolution-PDS_X.shape[0]
            PDS_X = active_case.X_direct_finder(Nneeded=Nneeded, pls_model=PLS_XY, current_pds=PDS_X)
        eval: plseval = PLS_XY.evaluation(xtes=PDS_X)
        PDS_Y, HT2, SPEX = eval.yfit, eval.HT2, eval.spex
        sort_metric = HT2/PLS_XY.T2_lim[-1] + (SPEX/PLS_XY.SPE_lim_x[-1] if PLS_XY.SPE_lim_x[-1] > 1e-6 else 0)
        sort_metric_idx = np.argsort(sort_metric)

        sort_metric = sort_metric[sort_metric_idx]
        PDS_X = PDS_X[sort_metric_idx]
        PDS_Y = PDS_Y[sort_metric_idx]

        Not_aggressive_sol = np.where(sort_metric < 2)[0]
        kept_sol_size = np.max((minNsolution, Not_aggressive_sol.size))
        PDS_X = PDS_X[:kept_sol_size]
        PDS_Y = PDS_Y[:kept_sol_size]

        # updating self
        current_itr.X_pds = PDS_X
        current_itr.Y_pds = PDS_Y

    def pds_prep_score_update(self, current_itr: ItreInfo):
        ''' calculate the prep score for any given x data'''
        # receive data
        x_pds = current_itr.X_pds
        y_pds = current_itr.Y_pds
        pls_model = current_itr.PLS_XY
        pca_model = current_itr.PCA_X
        scal_agent = current_itr.model_alignment_scal_agent
        what_metric = current_itr.what_metric
        w = current_itr.prep_sol['w']
        p = current_itr.prep_sol['p']

        # operation
        pds_alignment_normal = self.model_alignment_matrix(pls_model, pca_model, x_pds)
        pds_alignment_normal = pds_alignment_normal[:, what_metric]
        pds_alignment_scaled = pds_alignment_normal/scal_agent

        pds_prep_score: np.ndarray = np.sum(w*(pds_alignment_scaled**p), axis=1)

        sorted_idx = pds_prep_score.argsort()
        pds_prep_score = pds_prep_score[sorted_idx]
        x_pds = x_pds[sorted_idx]
        y_pds = y_pds[sorted_idx]
        # Updating
        current_itr.pds_alignment_normal = pds_alignment_normal[sorted_idx]
        current_itr.pds_alignment_scaled = pds_alignment_scaled[sorted_idx]
        current_itr.X_pds = x_pds
        current_itr.Y_pds = y_pds
        current_itr.pds_prep_score = pds_prep_score
        lprep = x_pds[0].reshape(1, -1)
        hprep = x_pds[-1].reshape(1, -1)
        current_itr.xitr_ = np.vstack((lprep, hprep))

    def prep_property_update(self):
        ''' It should complete the self_xitr and self_yitre and model assessment based on self_itre history'''
        XYitr = self.itr_dict_
        assessment = np.empty((0, 1))
        x_itr = np.empty((0, self.active_case.X.shape[1]))
        y_itr = np.empty((0, self.active_case.Y.shape[1]))
        updated_XYitr = {0: {'x': None, 'y': None, 'accuracy': 'NA'}}

        n_available_itr = len(self.itr_history)
        for i in range(1, n_available_itr+1):
            assessment = np.vstack((assessment, XYitr[i]['accuracy']))
            x_itr = np.vstack((x_itr, XYitr[i]['x']))
            y_itr = np.vstack((y_itr, XYitr[i]['y']))
            updated_XYitr[i] = {'x': XYitr[i]['x'], 'y': XYitr[i]['y'], 'accuracy': XYitr[i]['accuracy']}

        self.iteration_assessment = assessment
        self.x_itr = x_itr
        self.y_itr = y_itr
        self.itr_dict_ = updated_XYitr
        self.itr = n_available_itr

    def go_to_itr(self, go_to_itr: int = 0):
        self.itr_history = self.itr_history[:go_to_itr]
        self.prep_property_update()

    class ItreInfo():
        def __init__(self, prep_model: PrepModel):
            self.what_metric = prep_model.what_metric
            self.Y_des = prep_model.Y_des
            self.active_case = prep_model.active_case
            self.prep_components = ['HT_{pls}', 'spex_{pls}', 'h_{pls}', 'HT_{pca}', 'spex_{pca}', 'h_{pca}']
            self.X_train = np.empty((0, prep_model.X_org.shape[1]), dtype=float)
            self.Y_train = np.empty((0, prep_model.Y_org.shape[1]), dtype=float)
            self.X_val = np.empty((0, prep_model.X_org.shape[1]), dtype=float)
            self.Y_val = np.empty((0, prep_model.Y_org.shape[1]), dtype=float)
            self.PLS_XY: pls = pls()
            self.PCA_X: pca = pca()
            self.model_alignment = np.empty((prep_model.Knn, self.what_metric.size), dtype=float)
            self.model_alignment_scal_agent = np.zeros_like(self.what_metric)
            self.model_alignment_scalled = np.zeros_like(self.model_alignment)
            self.model_alignment_PA = np.zeros_like(prep_model.Knn)

            self.prep_eq = None
            self.prep_cost = None
            self.prep_sol = None
            self.optim_model = None

            self.X_pds = None
            self.Y_pds = None
            self.pds_alignment_normal = None
            self.pds_alignment_scaled = None
            self.pds_prep_score = None
            self.xitr_ = None
            self.yitr_ = None
            self.pds_all_in_one = None

        def __repr__(self):
            return f'Num PDS:{self.X_pds.shape[0]},prep cost:{self.prep_cost},x_itr:{self.xitr_} '

        def show(self):
            self.plot_prep_optim()
            print(f'Y_desirable is {self.Y_des}')
            vicinity_neighbors = 3
            geometric_metric = self.active_case.geometric_info(
                pds=self.X_pds, latent_space=True, vicinity_neighbors=vicinity_neighbors)
            PDS_all_in_one = np.hstack((self.X_pds, self.Y_pds, self.pds_prep_score.reshape(-1, 1), geometric_metric))
            col_label = ['input ' + str(i+1) for i in range(self.X_train.shape[1])] +\
                ['output ' + str(i+1) for i in range(self.Y_train.shape[1])] + ['Prep Score'] + \
                [f'mean closeness( {vicinity_neighbors} nn)', f'mean distance( {vicinity_neighbors} nn)']
            df = pd.DataFrame(PDS_all_in_one, columns=col_label)
            pd.set_option('display.width', 1000)
            print(df.round(2))
            self.pds_all_in_one = PDS_all_in_one

        def plot_prep_optim(self):
            '''current itre ploting'''

            # Receiving data
            pds_prep_score = self.pds_prep_score
            prep_score = self.prep_sol['Prep Score [fitted]']
            accuracy = self.prep_sol['accuracy']
            C = self.prep_components
            W = self.prep_sol['w']
            W[W < 1e-3] = 0
            P = self.prep_sol['p']
            P[P < 1e-3] = 0
            title = ' + '.join([f'{W[i]:.2f} \\cdot {C[i]}^{{{P[i]:.2f}}}' for i in range(W.size)])

            slope_fit, intercept_fit = np.polyfit(prep_score, accuracy, 1)
            x_data = np.arange(0, 1, .05)
            y_data = slope_fit*x_data + intercept_fit
            plt.figure()
            plt.scatter(prep_score, accuracy, marker='o', label='validation data')
            plt.plot(x_data, y_data, 'k--', label='PREP fitted line')
            plt.scatter(pds_prep_score, np.zeros(pds_prep_score.size), marker='*', s=100)
            plt.title(f'${title}$,optim Cost = {self.prep_cost:.3f}', fontsize=12)
            plt.show()

        def plot_x_y_prep(self, prep_model: PrepModel):
            cmap_type = 'copper'

            def inner_plotter(x, y, x_pds, y_pds, xlabel, ylabel, y_des, y_des_range, metric, position, vmin, vmax):
                # Fit a line to the data
                slope_fit, intercept_fit = np.polyfit(x, y, 1)
                x_data = np.arange(x.min(), x.max(), .05)
                y_data = slope_fit * x_data + intercept_fit
                y_hat = slope_fit * x + intercept_fit

                # Calculate R²
                y_mean = np.mean(y)  # Mean of y
                SS_total = np.sum((y - y_mean)**2)  # Total sum of squares
                SS_residual = np.sum((y - y_hat)**2)  # Residual sum of squares
                R2 = 1 - (SS_residual / SS_total)  # R²

                # Create the subplot
                ax = plt.subplot(n_row, n_col, position)
                scatter = ax.scatter(x_pds, y_pds, s=20, c=metric, vmin=vmin, vmax=vmax,
                                     cmap=cmap_type)  # Use consistent vmin/vmax
                ax.scatter(x, y, marker='s', s=50, c='red')
                ax.plot(x_data, y_data, 'b-')

                # Add horizontal lines for y_des and y_des_range
                ax.plot([x_data.min(), x_data.max()], [y_des - y_des_range, y_des - y_des_range], 'k--')
                ax.plot([x_data.min(), x_data.max()], [y_des + y_des_range, y_des + y_des_range], 'k--')
                ax.set_title(f'R2 = {R2:.2f}')

            X_all = self.X_train
            Y_all = self.Y_train

            y_des = prep_model.Y_des.reshape(-1)
            y_des_range = prep_model.Y_des_range.reshape(
                -1) if prep_model.Y_des_range is not None else .05 * Y_all.mean(axis=0)

            prep_score = self.pds_prep_score
            pds_x = self.X_pds
            pds_y = self.Y_pds

            x_var_label = prep_model.active_case.xvarlabel
            y_var_label = prep_model.active_case.yvarlabel

            n_col, n_row = X_all.shape[1], Y_all.shape[1]
            position = 1

            fig = plt.figure(figsize=(10, 8))  # Adjust figure size as needed

            # Define the global min and max for the metric (to ensure consistent colormap across subplots)
            vmin = np.min(prep_score)
            vmax = np.max(prep_score)

            # Plot each subplot
            for i in range(n_row):
                for j in range(n_col):
                    inner_plotter(x=X_all[:, j], y=Y_all[:, i], x_pds=pds_x[:, j], y_pds=pds_y[:, i],
                                  xlabel=x_var_label[j], ylabel=y_var_label[i], metric=prep_score, position=position,
                                  y_des=y_des[i], y_des_range=y_des_range[i], vmin=vmin, vmax=vmax)
                    plt.xlabel(x_var_label[j]) if i == n_row-1 else None
                    plt.ylabel(y_var_label[i]) if j == 0 else None
                    position += 1

            plt.tight_layout()
            sm = ScalarMappable(cmap=cmap_type, norm=Normalize(vmin=vmin, vmax=vmax))
            sm.set_array([])

            cbar_ax = fig.add_axes([0.92, 0.15, 0.02, 0.7])
            cbar = fig.colorbar(sm, cax=cbar_ax)
            cbar.set_label('PREP Score')

            plt.subplots_adjust(right=0.85)
            plt.show()
