import numpy as np
import matplotlib.pyplot as plt
import time
from LVMI.util.prediction_accuracy import prediction_accuracy
from typing import Dict, List, Callable, Any, Tuple, Union
from dataclasses import dataclass
from LVM.pca import PcaClass as pca


@dataclass
class MethodResult:
    accuracy: float
    iterations: int
    execution_time: float
    final_y: np.ndarray
    reached_threshold: bool


@dataclass
class TrialResult:
    trial_id: int
    y_des: np.ndarray
    difficulty_metrics: Dict[str, float]
    methods: Dict[str, MethodResult]


class InversionAssessment:
    def __init__(self, active_case: Any):
        self.active_case = active_case

    def _calculate_accuracy(self, y_result: np.ndarray) -> float:
        """
        Calculate accuracy based on the result and desired output.
        Override this method if you need a different accuracy calculation.
        """
        return prediction_accuracy(target=self.active_case.Y_des, tried=y_result, Y_org=self.active_case.Y, RorMse=1)

    def compare_methods(
        self,
        methods: Dict[str, Callable],
        n_trials: int = 5,
        n_iterations: int = 5,
        accuracy_threshold: float = 0.95
    ) -> <PERSON><PERSON>[Dict, List[TrialResult]]:
        """
        Compare different inversion methods using multiple trials with different Y_des targets

        Args:
            methods: Dictionary of method names and their corresponding functions
            n_trials: Number of different Y_des targets to test
            n_iterations: Maximum number of iterations for each method
            accuracy_threshold: Stop iterations when this accuracy is reached (0 to 1)

        Returns:
            tuple: (summary dict, detailed_results list)
        """
        results = {
            method_name: {'accuracy': [], 'iterations': [], 'time': []}
            for method_name in methods.keys()
        }

        detailed_results = []

        for trial in range(n_trials):
            Y_des_trial = self.active_case.suggest_y_des(knn=5, clossness=.8)[1]
            self.active_case.Y_des = Y_des_trial.reshape(1, -1)

            pca_model = pca().fit(X=self.active_case.Y)
            pca_eval = pca_model.evaluation(self.active_case.Y_des)
            ht2_ratio = pca_eval.HT2 / pca_model.T2_lim[-1]
            spe_ratio = pca_eval.spe / pca_model.SPE_lim_x[-1] if pca_model.SPE_lim_x[-1] > 1e-6 else 0

            trial_results = TrialResult(
                trial_id=trial,
                y_des=Y_des_trial,
                difficulty_metrics={'ht2_ratio': ht2_ratio, 'spe_ratio': spe_ratio},
                methods={}
            )

            for method_name, method_func in methods.items():
                start_time = time.time()
                try:
                    current_accuracy = 0
                    itr_count = 0
                    final_result = None

                    while current_accuracy < accuracy_threshold and itr_count < n_iterations:
                        # Execute the method
                        raw_result = method_func()
                        y_result = self.active_case.y_calculator(raw_result)
                        accuracy = self._calculate_accuracy(y_result)

                        # Update iteration count and check accuracy
                        itr_count += 1
                        current_accuracy = float(accuracy.max())
                        final_result = y_result

                        if current_accuracy >= accuracy_threshold:
                            print(f"{method_name} reached accuracy threshold after {itr_count} iterations")
                            break

                    elapsed_time = time.time() - start_time

                    method_result = MethodResult(
                        accuracy=current_accuracy,
                        iterations=itr_count,
                        execution_time=elapsed_time,
                        final_y=final_result,
                        reached_threshold=current_accuracy >= accuracy_threshold
                    )

                    results[method_name]['accuracy'].append(method_result.accuracy)
                    results[method_name]['iterations'].append(method_result.iterations)
                    results[method_name]['time'].append(method_result.execution_time)

                    trial_results.methods[method_name] = method_result

                except Exception as e:
                    print(f"Error in method {method_name}: {str(e)}")
                    continue

                print(f"{method_name}, trial: {trial}, in {itr_count} itr, Accuracy:{current_accuracy:.2f}")

            detailed_results.append(trial_results)

        summary = self._compute_summary(results, n_trials, accuracy_threshold)

        self._plot_method_comparison(summary)
        self._plot_detailed_analysis(detailed_results)

        return summary, detailed_results

    def _compute_summary(self, results: Dict, n_trials: int, accuracy_threshold: float) -> Dict:
        summary = {}
        for method in results:
            if results[method]['accuracy']:
                summary[method] = {
                    'mean_accuracy': np.mean(results[method]['accuracy']),
                    'std_accuracy': np.std(results[method]['accuracy']),
                    'mean_iterations': np.mean(results[method]['iterations']),
                    'mean_time': np.mean(results[method]['time']),
                    'success_rate': len([acc for acc in results[method]['accuracy'] if acc >= accuracy_threshold]) / n_trials
                }
        return summary

    def _plot_method_comparison(self, summary: Dict):
        """Plot comparison of different methods based on summary statistics"""
        methods = list(summary.keys())
        metrics = ['mean_accuracy', 'std_accuracy', 'mean_iterations', 'mean_time', 'success_rate']

        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()
        colors = ['blue', 'green', 'red', 'purple']

        for i, metric in enumerate(metrics):
            values = [summary[method][metric] for method in methods]
            ax = axes[i]
            bars = ax.bar(methods, values, color=colors[:len(methods)])

            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:.3f}',
                        ha='center', va='bottom')

            ax.set_title(metric.replace('_', ' ').title())
            ax.set_ylabel('Value')
            ax.grid(True, alpha=0.3)
            plt.setp(ax.get_xticklabels(), rotation=45)

        axes[-1].remove()
        plt.tight_layout()
        plt.show()

    def _plot_detailed_analysis(self, detailed_results: List[TrialResult]):
        """Plot detailed analysis of method performance vs problem difficulty"""
        difficulties = [r.difficulty_metrics['ht2_ratio'] for r in detailed_results]
        methods = list(detailed_results[0].methods.keys())

        plt.figure(figsize=(12, 6))
        colors = ['b', 'g', 'r', 'purple']
        markers = ['o', 's', '^', 'D']

        for method, color, marker in zip(methods, colors, markers):
            accuracies = []
            valid_difficulties = []

            for trial, diff in zip(detailed_results, difficulties):
                if method in trial.methods:
                    accuracies.append(trial.methods[method].accuracy)
                    valid_difficulties.append(diff)

            if accuracies:
                plt.scatter(valid_difficulties, accuracies,
                            label=method, color=color, marker=marker)

        plt.xlabel('Problem Difficulty (HT2 Ratio)')
        plt.ylabel('Accuracy')
        plt.title('Method Performance vs Problem Difficulty')
        plt.legend()
        plt.grid(True)
        plt.show()

        self._plot_best_methods_by_difficulty(detailed_results, difficulties, methods)

    def _plot_best_methods_by_difficulty(self, detailed_results: List[TrialResult],
                                         difficulties: List[float], methods: List[str]):
        difficulty_ranges = [(0, 0.3), (0.3, 0.8), (0.8, float('inf'))]
        range_labels = ['Easy', 'Medium', 'Hard']
        best_methods = {label: {method: 0 for method in methods} for label in range_labels}

        for trial, diff in zip(detailed_results, difficulties):
            range_idx = next(i for i, (low, high) in enumerate(difficulty_ranges)
                             if low <= diff < high)

            best_acc = -float('inf')
            best_method = None

            for method in methods:
                if method in trial.methods:
                    acc = trial.methods[method].accuracy
                    if acc > best_acc:
                        best_acc = acc
                        best_method = method

            if best_method:
                best_methods[range_labels[range_idx]][best_method] += 1

        plt.figure(figsize=(12, 6))
        x = np.arange(len(range_labels))
        width = 0.15

        for i, method in enumerate(methods):
            counts = [best_methods[label][method] for label in range_labels]
            plt.bar(x + i*width, counts, width, label=method)

        plt.xlabel('Problem Difficulty')
        plt.ylabel('Number of Times Best')
        plt.title('Best Performing Method by Difficulty Level')
        plt.xticks(x + width*1.5, range_labels)
        plt.legend()
        plt.show()
