from .method_comparison import InversionAssessment
from Projects.simulated_data.sim_data import SimData
active_case = SimData(N=30, case_id=1, xvar=3, yvar=2)
# Create assessment instance
assessment = InversionAssessment(active_case)

# Define methods to compare
methods = {
    'direct': lambda: active_case.apply_direct(ploting=False),
    'direct_newMI': lambda: active_case.apply_new_direct(ploting=False),
    'ibo': lambda: active_case.apply_ibo(ploting=False),
    'prep': lambda: active_case.apply_prep(ploting=False),
    # 'pds_no_prep': lambda: active_case.apply_pds_ranking_dis_no_prep(ploting=False),
    # 'pds_with_prep': lambda: active_case.apply_pds_ranking_dis_with_prep(ploting=False),
}

# Run comparison
summary, detailed_results = assessment.compare_methods(
    methods=methods,
    n_trials=10,
    n_iterations=1,
    accuracy_threshold=0.95
)
