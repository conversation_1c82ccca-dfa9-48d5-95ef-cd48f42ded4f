<mxfile host="65bd71144e">
    <diagram id="5pNX2USA4fDTeIJD3oNJ" name="Page-1">
        <mxGraphModel dx="1674" dy="827" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1100" pageHeight="850" math="0" shadow="1">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="7" value="Active Case" style="swimlane;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=20;fontStyle=0" parent="1" vertex="1">
                    <mxGeometry x="90" y="50" width="520" height="700" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="&lt;font&gt;Data set insertion&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=20;fontStyle=0" parent="7" vertex="1">
                    <mxGeometry x="10" y="40" width="480" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-41" value="&lt;font&gt;apply condition&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=20;fontStyle=0" parent="7" vertex="1">
                    <mxGeometry x="11" y="170" width="480" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-42" value="Xdirect finder" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=20;fontStyle=0" parent="7" vertex="1">
                    <mxGeometry x="11" y="250" width="480" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-48" value="Dist order" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=20;fontStyle=0" parent="7" vertex="1">
                    <mxGeometry x="11" y="331" width="480" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-54" value="Assessment" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=20;fontStyle=0" parent="7" vertex="1">
                    <mxGeometry x="11" y="410" width="480" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-64" value="Itre update" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=20;fontStyle=0" parent="7" vertex="1">
                    <mxGeometry x="11" y="490" width="480" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-67" value="Execution (either prep or any other)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=20;fontStyle=0" parent="7" vertex="1">
                    <mxGeometry x="12" y="570" width="480" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="PREP Model methods" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=20;fontStyle=0" parent="1" vertex="1">
                    <mxGeometry x="740" y="60" width="490" height="620" as="geometry"/>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-25" value="&lt;span style=&quot;color: rgb(63, 63, 63);&quot;&gt;PREP implementation&lt;/span&gt;" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#CC99FF;fontSize=20;" parent="11" vertex="1">
                    <mxGeometry x="15" y="130" width="460" height="120" as="geometry">
                        <mxRectangle x="10" y="200" width="460" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-26" value="model alignment&amp;nbsp;" style="text;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=20;fontStyle=0" parent="2vHQCHj7SctIibT5Pgfa-25" vertex="1">
                    <mxGeometry y="30" width="460" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-27" value="optimization pre steps" style="text;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=20;fontStyle=0" parent="2vHQCHj7SctIibT5Pgfa-25" vertex="1">
                    <mxGeometry y="60" width="460" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-28" value="Optimization algorithm and cost" style="text;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=20;fontStyle=0" parent="2vHQCHj7SctIibT5Pgfa-25" vertex="1">
                    <mxGeometry y="90" width="460" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-36" value="&lt;span style=&quot;color: rgb(63, 63, 63);&quot;&gt;PDS Update&lt;/span&gt;" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#CC99FF;fontSize=20;" parent="11" vertex="1">
                    <mxGeometry x="10" y="280" width="460" height="120" as="geometry">
                        <mxRectangle x="10" y="200" width="460" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-37" value="Calculate pds" style="text;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=20;fontStyle=0" parent="2vHQCHj7SctIibT5Pgfa-36" vertex="1">
                    <mxGeometry y="30" width="460" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-38" value="updatate calculate their prep score" style="text;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=20;fontStyle=0" parent="2vHQCHj7SctIibT5Pgfa-36" vertex="1">
                    <mxGeometry y="60" width="460" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-39" value="selecect the L-PREP and high PREP" style="text;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=20;fontStyle=0" parent="2vHQCHj7SctIibT5Pgfa-36" vertex="1">
                    <mxGeometry y="90" width="460" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-35" value="&lt;span&gt;Recording itreation info&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=20;fontStyle=0" parent="11" vertex="1">
                    <mxGeometry x="10" y="425" width="460" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="2vHQCHj7SctIibT5Pgfa-68" value="&lt;span&gt;X_itr is a proprti of the prep not the whole case&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1ba1e2;strokeColor=#006EAF;fontSize=25;fontStyle=1;fontColor=#ffffff;" parent="11" vertex="1">
                    <mxGeometry x="10" y="510" width="460" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="&lt;font&gt;Knn&lt;/font&gt;&lt;div&gt;&lt;font&gt;(check last itre)&lt;/font&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=20;fontStyle=0" parent="11" vertex="1">
                    <mxGeometry x="15" y="40" width="460" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="kboj35CRGK5-kELxQtkm" name="Page-2">
        <mxGraphModel dx="2316" dy="711" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1100" pageHeight="850" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-2" value="&lt;span style=&quot;color: rgb(63, 63, 63); font-weight: 700;&quot;&gt;what a dataset needs to have&lt;/span&gt;" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#33FFFF;fontSize=18;align=center;" vertex="1" parent="1">
                    <mxGeometry x="-20" y="120" width="380" height="240" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-3" value="X, Y" style="text;strokeColor=none;fillColor=#33FFFF;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=18;" vertex="1" parent="FPD162XkSIRxV_OW6EBJ-2">
                    <mxGeometry y="30" width="380" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-4" value="X_itr,Y_itr" style="text;strokeColor=none;fillColor=#33FFFF;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=18;" vertex="1" parent="FPD162XkSIRxV_OW6EBJ-2">
                    <mxGeometry y="60" width="380" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-5" value="Y_des" style="text;strokeColor=none;fillColor=#33FFFF;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=18;" vertex="1" parent="FPD162XkSIRxV_OW6EBJ-2">
                    <mxGeometry y="90" width="380" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-6" value="Y_des range&lt;span style=&quot;white-space: pre;&quot;&gt;&#x9;&lt;/span&gt;" style="text;strokeColor=none;fillColor=#33FFFF;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=18;" vertex="1" parent="FPD162XkSIRxV_OW6EBJ-2">
                    <mxGeometry y="120" width="380" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-7" value="Y_des range&lt;span style=&quot;white-space: pre;&quot;&gt;&#x9;&lt;/span&gt;" style="text;strokeColor=none;fillColor=#33FFFF;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=18;" vertex="1" parent="FPD162XkSIRxV_OW6EBJ-2">
                    <mxGeometry y="150" width="380" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-8" value="Y_des range&lt;span style=&quot;white-space: pre;&quot;&gt;&#x9;&lt;/span&gt;" style="text;strokeColor=none;fillColor=#33FFFF;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=18;" vertex="1" parent="FPD162XkSIRxV_OW6EBJ-2">
                    <mxGeometry y="180" width="380" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-9" value="Y_des range&lt;span style=&quot;white-space: pre;&quot;&gt;&#x9;&lt;/span&gt;" style="text;strokeColor=none;fillColor=#33FFFF;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=18;" vertex="1" parent="FPD162XkSIRxV_OW6EBJ-2">
                    <mxGeometry y="210" width="380" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-10" value="&lt;span style=&quot;color: rgb(63, 63, 63); font-weight: 700;&quot;&gt;What is specific for each case&lt;/span&gt;" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#33FFFF;fontSize=18;align=center;" vertex="1" parent="1">
                    <mxGeometry x="430" y="120" width="380" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-11" value="ploting data" style="text;strokeColor=none;fillColor=#33FFFF;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=18;" vertex="1" parent="FPD162XkSIRxV_OW6EBJ-10">
                    <mxGeometry y="30" width="380" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-12" value="&lt;font&gt;selection of neighbors method&lt;/font&gt;" style="text;strokeColor=none;fillColor=#33FFFF;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=18;" vertex="1" parent="FPD162XkSIRxV_OW6EBJ-10">
                    <mxGeometry y="60" width="380" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-13" value="x_direct finder algorithm" style="text;strokeColor=none;fillColor=#33FFFF;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=18;" vertex="1" parent="FPD162XkSIRxV_OW6EBJ-10">
                    <mxGeometry y="90" width="380" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-19" value="apply condition on X" style="text;strokeColor=none;fillColor=#33FFFF;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=18;" vertex="1" parent="FPD162XkSIRxV_OW6EBJ-10">
                    <mxGeometry y="120" width="380" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-20" value="&lt;span style=&quot;color: rgb(63, 63, 63); font-weight: 700;&quot;&gt;What is shared for all cases&lt;/span&gt;" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#33FFFF;fontSize=18;align=center;" vertex="1" parent="1">
                    <mxGeometry x="440" y="320" width="380" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-21" value="Method nameing (apply condition,etc..)" style="text;strokeColor=none;fillColor=#33FFFF;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=18;" vertex="1" parent="FPD162XkSIRxV_OW6EBJ-20">
                    <mxGeometry y="30" width="380" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="FPD162XkSIRxV_OW6EBJ-25" style="text;strokeColor=none;fillColor=#33FFFF;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontSize=18;" vertex="1" parent="FPD162XkSIRxV_OW6EBJ-20">
                    <mxGeometry y="60" width="380" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>