from LVMI.inversion_methods.pds_ranking_dis import PDS_ranking_dis
from LVMI.inversion_methods.ibo import IbO
from LVMI.inversion_methods.new_direct_inversion import NewDirectInversion
from LVMI.inversion_methods.direct_inversion import DirectInversion
from LVMI.util.class_archiver import *
from LVMI.util.prediction_accuracy import prediction_accuracy as prediction_accuracy

import numpy as np
import ast
import matplotlib.pyplot as plt

''' From src'''
from LVM.pls import PlsClass as pls, plseval
from LVM.pca import PcaClass as pca, pcaeval
from optim.pso import Pso
from sklearn.metrics import pairwise_distances
from abc import ABC, abstractmethod
from datetime import datetime


class Problem:
    def __init__(self):
        self.X = None
        self.Y = None
        self.Y_des = None
        self.Y_des_range = None
        self.Xmin = None
        self.steps = None
        self.Xmax = None
        self.xvarlabel = None
        self.yvarlabel = None
        self.case_id = None
        self.Y_des_importance = None

        self.prep_model = None
        self.direct_inversion_model = None
        self.new_direct_inversion_model = None
        self.ibo_model = None
        self.pds_ranking_dis_model_with_prep = None
        self.pds_ranking_dis_model_no_prep = None
        self.all_inversion_results = {}

        # general initialization
        self.archive_path = None

    def applying_conditions_X(self, x_data, y_data=None) -> tuple[np.ndarray, np.ndarray]:
        """Apply condition over the x_data make sure they are within the range and also make sure y_data has psitive values

        Args:
            PDS_x (np.ndarray): x_data
            PDS_y (np.ndarray): y_data

        Returns:
            x_data: filtered x_data, y_data
        """
        Xmin = self.Xmin if self.Xmin.size == x_data.shape[1] else self.Xmin*np.ones(x_data.shape[1])
        Xmax = self.Xmax if self.Xmax.size == x_data.shape[1] else self.Xmax*np.ones(x_data.shape[1])

        if y_data is None:
            y_data = x_data
        else:
            cond = np.unique(np.where(y_data < 0)[0])
            x_data = np.delete(x_data, cond, axis=0)
            y_data = np.delete(y_data, cond, axis=0)

        for i in range(Xmin.size):
            lb = Xmin[i]
            ub = Xmax[i]
            if x_data.size > 0:
                data = x_data[:, i]
                condi = np.unique(np.where((data < lb) | (data > ub))[0])
                x_data = np.delete(x_data, condi, axis=0)
                y_data = np.delete(y_data, condi, axis=0)
        return x_data, y_data

    def nearest_neighbors_update(self, data, target_point, scalled=True, scal_ref=None, metric: str = 'euclidean', knn=5, visualplot=False):
        """sort the index of samples based on their closeness to the target

        Args:
            data (_type_): _description_
            target_point (_type_): _description_
            scalled (bool, optional): _description_. Defaults to True.
            xRef (_type_, optional): _description_. Defaults to None.
            metric (str, optional): _description_. Defaults to 'euclidean'.

        Returns:
            np.ndarray: sorted_arg
        """

        scal_ref = data if scal_ref is None else scal_ref
        if scalled is True:
            C, S = np.mean(scal_ref, axis=0), np.std(scal_ref, axis=0, ddof=1)
            data, target_point = (data - C) / S, (target_point - C) / S

        dis_from_target = pairwise_distances(data, target_point, metric=metric)
        sorted_idx = dis_from_target.reshape(-1).argsort()
        if visualplot:
            self.output_space_plot(Y_train=data, y_local=data[sorted_idx[:knn]], y_des=target_point)
            suggested_sorted_idx = ast.literal_eval(input('enter your suggestion list [1,2,3] or [] = '))
            if len(suggested_sorted_idx) > 0:
                sorted_idx = np.array(suggested_sorted_idx)
        return sorted_idx

    def knn_update(self, x_itr: np.ndarray = None, y_itr: np.ndarray = None, knn=5, y_des=None, **kwargs):
        X_all = np.vstack((self.X, x_itr)) if x_itr is not None else self.X
        Y_all = np.vstack((self.Y, y_itr)) if y_itr is not None else self.Y
        Y_des = self.Y_des if y_des is None else y_des
        sorted_idx = self.nearest_neighbors_update(data=Y_all, target_point=Y_des, knn=knn, **kwargs)[:knn]

        X_local = X_all[sorted_idx]
        Y_local = Y_all[sorted_idx]

        if x_itr is not None and x_itr.shape[0] > 0:
            last_itr_x = x_itr[-2:]
            last_itr_y = y_itr[-2:]
            flag_of_inclusion = np.any(np.all(last_itr_x[:, None] == X_local, axis=2))

            if not flag_of_inclusion:
                # print('recent_iteration was manually added')
                sorted_last_itr = self.nearest_neighbors_update(
                    last_itr_y, self.Y_des, scal_ref=self.Y, scalled=True)

                to_be_added_x = last_itr_x[sorted_last_itr[0]].reshape(1, -1)
                to_be_added_y = last_itr_y[sorted_last_itr[0]].reshape(1, -1)

                random_selection_for_delete = np.random.randint(knn)
                X_local = np.delete(X_local, random_selection_for_delete, axis=0)
                X_local = np.vstack((X_local, to_be_added_x))

                Y_local = np.delete(Y_local, random_selection_for_delete, axis=0)
                Y_local = np.vstack((Y_local, to_be_added_y))
        return X_local, Y_local

    def X_direct_finder(self, pls_model, Nneeded, current_pds):
        ''' To be determined by Children'''
        for k in range(Nneeded):
            CostFcnParam = {'pls_model': pls_model,
                            'current_pds': current_pds,
                            'y_des': self.Y_des,
                            'L': self.Y_des_importance if self.Y_des_importance is not None else np.ones(self.Y_des.shape[1]),
                            'apply_cond_fcn': self.applying_conditions_X}
            my_problem = Pso.OptimProblem(CostFcn=self.X_direct_cost, nVar=self.X.shape[1], VarMin=self.Xmin,
                                          VarMax=self.Xmax, CostFcnParam=CostFcnParam)

            pso_model: Pso = Pso(problem=my_problem, pso_params={'wdamp': .99}, general_params={
                'MaxIt': 100, 'nPop': 50, 'print': False})
            current_pds = np.vstack((current_pds, pso_model.best.Position))

        return current_pds

    @staticmethod
    def X_direct_cost(x_suggested, CostFcnParam):
        ''' receive X, and return cost and sol '''
        CostFcnParam['nfe'] += 1
        x_suggested = x_suggested.reshape(1, -1)
        pls_model: pls = CostFcnParam['pls_model']
        current_pds = CostFcnParam['current_pds']
        y_des = CostFcnParam['y_des']
        applying_condition = CostFcnParam['apply_cond_fcn']
        L = CostFcnParam['L']

        Y_org = pls_model.Ytrain_normal
        C, S = Y_org.mean(axis=0), Y_org.std(axis=0, ddof=1)

        y_pre: plseval = pls_model.evaluation(xtes=x_suggested).yfit
        x_suggested, y_pre = applying_condition(x_suggested, y_pre)
        if x_suggested.size < 1:
            return np.inf, []
        else:
            all_x_data = np.vstack((pls_model.Xtrain_normal, current_pds))

            min_distances_cond = np.linalg.norm(x_suggested - all_x_data, axis=1).min() >= .05*all_x_data.mean()
            penalty = 0 if min_distances_cond else 100

            y_des_scla = (y_des-C)/(S+1e-15)
            y_pre_scla = (y_pre-C)/(S+1e-15)
            e = y_des_scla-y_pre_scla
            cost = <EMAIL>(L)@e.T + penalty
            # cost = np.sum(np.abs(Y_des_scla-y_pre_scla)) + penalty
            return cost, []

    ''' Inversions '''

    def apply_direct(self, knn: int = None, npls: int = None, knn_visualplot=False):
        '''this one applies the original MI on the dataset in order to find the correct answer'''
        direct_inversion_model = DirectInversion(
            active_case=self, Npls_component=npls, Knn=knn, Npca_component=None) if self.direct_inversion_model is None else self.direct_inversion_model

        direct_inversion_model.execute(knn_visualplot=knn_visualplot)
        self.direct_inversion_model = direct_inversion_model
        return direct_inversion_model.x_itr[-1]

    def apply_new_direct(self, knn: int = None, npls: int = None, knn_visualplot=False):
        new_direct_inversion_model = NewDirectInversion(
            active_case=self, Npls_component=npls, Knn=knn, Npca_component=None) if self.new_direct_inversion_model is None else self.new_direct_inversion_model

        new_direct_inversion_model.execute(knn_visualplot=knn_visualplot)
        self.new_direct_inversion_model = new_direct_inversion_model
        return new_direct_inversion_model.x_itr[-1]

    def apply_ibo(self, knn: int = None, npls: int = None, optim_param={'nPop': 200, 'MaxIt': 100, 'print': False}, knn_visualplot=False, y_des_importance=None):

        ibo_model = IbO(active_case=self, Npls_component=npls, Knn=knn,
                        Npca_component=None) if self.ibo_model is None else self.ibo_model

        ibo_model.execute(optim_param=optim_param, knn_visualplot=knn_visualplot, y_des_importance=y_des_importance)
        self.ibo_model = ibo_model
        return ibo_model.x_itr[-1]

    def apply_prep(self, *argval, **argname):
        from LVMI.inversion_methods.prep_model import PrepModel
        prep_model = PrepModel(active_case=self, *argval, **argname) if self.prep_model is None else self.prep_model
        prep_model.execute()
        self.prep_model = prep_model
        return prep_model.itr_history[-1].xitr_

    def apply_pds_ranking_dis_with_prep(self, prep_model, vicinity_neighbors=3, y_des=None, prep_inclusion_in_ranking=True):

        pds_ranking_dis_model_with_prep = PDS_ranking_dis(
            active_case=self) if self.pds_ranking_dis_model_with_prep is None else self.pds_ranking_dis_model_with_prep

        pds_ranking_dis_model_with_prep.execute(
            prep_model=prep_model, vicinity_neighbors=vicinity_neighbors, y_des=y_des, prep_inclusion_in_ranking=prep_inclusion_in_ranking)

        self.pds_ranking_dis_model_with_prep = pds_ranking_dis_model_with_prep
        return pds_ranking_dis_model_with_prep.x_itr[-1]

    def apply_pds_ranking_dis_no_prep(self, prep_model, vicinity_neighbors=3, y_des=None, prep_inclusion_in_ranking=False):

        pds_ranking_dis_model_no_prep = PDS_ranking_dis(
            active_case=self) if self.pds_ranking_dis_model_no_prep is None else self.pds_ranking_dis_model_no_prep

        pds_ranking_dis_model_no_prep.execute(
            prep_model=prep_model, vicinity_neighbors=vicinity_neighbors, y_des=y_des, prep_inclusion_in_ranking=prep_inclusion_in_ranking)

        self.pds_ranking_dis_model_no_prep = pds_ranking_dis_model_no_prep
        return pds_ranking_dis_model_no_prep.x_itr[-1]

    def all_inversion(self, knn: int = 5, npls: int = None, x_itr: np.ndarray = None, y_itr: np.ndarray = None, ploting=False):
        '''Intelligently selects inversion method based on historical data analysis'''
        npls = self.X.shape[1] if npls is None else npls
        knn = npls+2 if knn < npls+2 else knn
        methods_labels = []
        Y_des = self.Y_des
        x_itr = np.empty((0, self.X.shape[1])) if x_itr is None else x_itr
        y_itr = np.empty((0, self.Y.shape[1])) if y_itr is None else y_itr

        x_train, y_train = self.knn_update(x_itr=x_itr, y_itr=y_itr, y_des=Y_des, knn=knn)
        pls_model = pls().fit(x_train, y_train, n_component=npls)

        x_sugg_all = np.empty((0, self.X.shape[1]))

        # direct inversion
        x_sugg_direct = self.apply_direct(knn=knn, npls=npls).reshape(1, -1)
        methods_labels.append('direct')
        x_sugg_all = np.vstack((x_sugg_all, x_sugg_direct))

        # direct inversion with new MI

        x_sugg_direct_newMI = self.apply_new_direct(knn=knn, npls=npls).reshape(1, -1)
        methods_labels.append('direct_newMI')
        x_sugg_all = np.vstack((x_sugg_all, x_sugg_direct_newMI))

        # ibo inversion
        x_sugg_ibo = self.apply_ibo(knn=knn, npls=npls, optim_param={
                                    'nPop': 500, 'MaxIt': 100, 'print': False}).reshape(1, -1)
        methods_labels.append('ibo')
        x_sugg_all = np.vstack((x_sugg_all, x_sugg_ibo))

        # prep inversion
        x_sugg_prep = self.apply_prep().reshape(-1, self.X.shape[1])
        methods_labels.append('L-prep')
        methods_labels.append('H-PREP')
        x_sugg_all = np.vstack((x_sugg_all, x_sugg_prep))

        # PDS_ distance-accuracy ranking
        X_pds_ranked_no_prep = self.apply_pds_ranking_dis_no_prep(
            prep_model=self.prep_model, prep_inclusion_in_ranking=False).reshape(1, -1)
        methods_labels.append('PDS_ranked_no_prep')
        x_sugg_all = np.vstack((x_sugg_all, X_pds_ranked_no_prep))

        X_pds_ranked_with_prep = self.apply_pds_ranking_dis_with_prep(
            prep_model=self.prep_model, prep_inclusion_in_ranking=True).reshape(1, -1)
        methods_labels.append('PDS_ranked_with_prep')
        x_sugg_all = np.vstack((x_sugg_all, X_pds_ranked_with_prep))

        methods_labels.append('avr')
        x_sugg_avr = x_sugg_all.mean(axis=0).reshape(1, -1)
        x_sugg_all = np.vstack((x_sugg_all, x_sugg_avr))

        self.all_inversion_results = {'x_sugg_all': x_sugg_all, 'methods_labels': methods_labels}
        if ploting:
            self.plot_suggestions_analysis(x_sugg_all, pls_model, Y_des, methods=methods_labels)

        output = {'x_sugg_all': x_sugg_all, 'methods_labels': methods_labels,
                  'pls_model': pls_model, 'prep_model': self.prep_model}
        return output
    '''Plots '''
    '''  X Only '''

    def geometric_info(self, pds, latent_space=True, vicinity_neighbors=3):
        ''' Calculate the average distance and closeness to its knn neighbors '''
        x_all = self.X
        y_all = self.Y
        y_des = self.Y_des
        y_all_closeness = prediction_accuracy(target=y_des, tried=y_all, Y_org=y_all, RorMse=1)

        pca_model = pca().fit(x_all) if latent_space else None

        if latent_space:
            x_all = pca_model.T
            pds = pca_model.evaluation(pds).tscore
        geometric_metric = np.zeros((pds.shape[0], 2))
        for i in range(pds.shape[0]):
            distance = np.sum((pds[i] - x_all)**2, axis=1)
            nearest_idx = np.argsort(distance)[:vicinity_neighbors]
            clossness_of_nearest = y_all_closeness[nearest_idx]
            distance_of_nearest = distance[nearest_idx]
            geometric_metric[i] = np.array([clossness_of_nearest.mean(), distance_of_nearest.mean()])

        return geometric_metric

    def target_alignment_assessment(self, y_itr: np.ndarray = None, x_itr: np.ndarray = None, knn: int = 5, npls: int = None, npca: int = None, y_des=None):
        """
        Assess and visualize target alignment using 5 key metrics:
        1. PCA Hoteling T2 knn
        2. PCA SPE_knn
        3. PCA Hotelling T2 all
        4. PCA SPE_all
        5. closeness Accuracy
        """
        npls = self.X.shape[1] if npls is None else npls
        npca = npls-1 if npca is None and npls > 1 else npca
        knn = npls+2 if knn < npls+2 else knn
        Y_des = self.Y_des if y_des is None else y_des
        x_all = np.vstack((self.X, x_itr)) if x_itr is not None else self.X
        y_all = np.vstack((self.Y, y_itr)) if y_itr is not None else self.Y

        # Get local neighborhood data
        x_train, y_train = self.knn_update(x_itr=x_itr, y_itr=y_itr, y_des=Y_des, knn=knn)
        pa_training = prediction_accuracy(target=Y_des, tried=y_train, Y_org=y_all, RorMse=1)

        # PCA-based assessment (all)
        pca_model_all = pca().fit(X=y_all, n_component=npca)
        pca_eval_all: pcaeval = pca_model_all.evaluation(Y_des)
        Hotelin_T2_all = pca_eval_all.HT2/pca_model_all.T2_lim[-1]
        spe_all = pca_eval_all.spe/pca_model_all.SPE_lim_x[-1] if pca_model_all.SPE_lim_x[-1] > 1e-6 else 0
        lvm_metric_all = Hotelin_T2_all+spe_all if pca_model_all.SPE_lim_x[-1] > 1e-6 else Hotelin_T2_all

        # PCA-based assessment (knn)
        pca_model_knn = pca().fit(X=y_train, n_component=npca)
        pca_eval_knn: pcaeval = pca_model_knn.evaluation(Y_des)
        Hotelin_T2_knn = pca_eval_knn.HT2/pca_model_knn.T2_lim[-1]
        spe_knn = pca_eval_knn.spe/pca_model_knn.SPE_lim_x[-1] if pca_model_knn.SPE_lim_x[-1] > 1e-6 else 0
        lvm_metric = Hotelin_T2_knn+spe_knn if pca_model_knn.SPE_lim_x[-1] > 1e-6 else Hotelin_T2_knn

        # Create visualization with 5 subplots
        fig = plt.figure(figsize=(15, 10))
        plt.suptitle(f'Target Alignment Analysis for Case {self.case_id}\n' +
                     f'(X:{self.X.shape[1]} vars, Y:{self.Y.shape[1]} vars)', y=1.02)

        # 1. Hoteling T2 Plot
        ax1 = plt.subplot(231)
        ax1.scatter(np.arange(1, knn+1), pca_model_knn.tsquared[:, -1], c='blue', label='Samples')
        ax1.axhline(y=pca_eval_knn.HT2.reshape(1, -1), color='m', linestyle='--', label='Target')
        ax1.axhline(y=pca_model_knn.T2_lim[-1], color='r', linestyle='--', label='Hotelling T2 Limit')
        ax1.set_title(
            f'PCA (n_com={pca_model_knn.n_component}) Hoteling T2(knn:{knn})\n T2_lim={pca_model_knn.T2_lim[-1]:.2f},Normalized: {float(Hotelin_T2_knn):.2f}')
        ax1.legend()

        # 2. SPE Plot
        ax2 = plt.subplot(232)
        ax2.scatter(np.arange(1, knn+1), pca_model_knn.SPE_x[:, -1], c='blue', label='Samples')
        ax2.axhline(y=pca_eval_knn.spe.reshape(1, -1), color='m', linestyle='--', label='Target')
        ax2.axhline(y=pca_model_knn.SPE_lim_x[-1], color='r', linestyle='--', label='SPE Limit')

        ax2.set_title(
            f'PCA(n_com={pca_model_knn.n_component}) SPE (knn:{knn})\nSPE_lim={pca_model_knn.SPE_lim_x[-1]:.2f},Normalized: {float(spe_knn):.2f}')
        ax2.legend()

        ax3 = plt.subplot(233)
        y_data = pca_model_knn.tsquared[:, -1]/pca_model_knn.T2_lim[-1] + pca_model_knn.SPE_x[:, -1] / \
            pca_model_knn.SPE_lim_x[-1] if pca_model_knn.SPE_lim_x[-1] > 1e-6 else pca_model_knn.tsquared[:, -1] / \
            pca_model_knn.T2_lim[-1]
        ax3.scatter(np.arange(1, knn+1), y_data, c='blue', label='Samples')
        y_lim = pca_model_knn.T2_lim[-1] + \
            pca_model_knn.SPE_lim_x[-1] if pca_model_knn.SPE_lim_x[-1] > 1e-6 else pca_model_knn.T2_lim[-1]

        ax3.axhline(y=lvm_metric*y_lim, color='m', linestyle='--', label='Target')
        ax3.axhline(y=y_lim, color='r', linestyle='--', label='Limit')

        ax3.set_title(f'LMV Metric (knn:{knn})\nNormalized: {float(lvm_metric):.2f}')
        ax3.legend()

        ax4 = plt.subplot(234)
        x_range = np.arange(1, y_all.shape[0]+1)
        ax4.scatter(x_range, pca_model_all.tsquared[:, -1], c='blue', label='Samples')
        ax4.axhline(y=pca_eval_all.HT2.reshape(1, -1), color='m', linestyle='--', label='Target')
        ax4.axhline(y=pca_model_all.T2_lim[-1], color='r', linestyle='--', label='Hotelling T2 Limit')
        ax4.set_title(
            f'PCA(n_com={pca_model_all.n_component}) Hoteling T2(all data)\n T2_lim={pca_model_all.T2_lim[-1]:.2f},Normalized: {float(Hotelin_T2_all):.2f}')
        ax4.legend()
        # 2. SPE Plot
        ax5 = plt.subplot(235)
        ax5.scatter(x_range, pca_model_all.SPE_x[:, -1], c='blue', label='Samples')
        ax5.axhline(y=pca_eval_all.spe.reshape(1, -1), color='m', linestyle='--', label='Target')
        ax5.axhline(y=pca_model_all.SPE_lim_x[-1], color='r', linestyle='--', label='SPE Limit')

        ax5.set_title(
            f'PCA(n_com={pca_model_all.n_component}) SPE (all data)\n SPE_lim={pca_model_all.SPE_lim_x[-1]:.2f},Normalized: {float(spe_all):.2f}')
        ax5.legend()

        # 4. Prediction Accuracy
        ax6 = plt.subplot(236)
        ax6.bar(np.arange(len(pa_training)), pa_training, color='lightgreen')
        ax6.axhline(y=np.mean(pa_training), color='r', linestyle='--', label='Mean')
        ax6.set_title(f'Clossness to Target\nMean: {np.mean(pa_training):.2f},max:{np.max(pa_training):.2f}')
        ax6.legend()

        plt.tight_layout()
        plt.show()

        return {
            'hoteling_t2_knn': float(Hotelin_T2_knn),
            'spe_knn': float(spe_knn),
            'hoteling_t2_all': float(Hotelin_T2_all),
            'spe_all': float(spe_all),
            'max prediction_accuracy': pa_training.max()
        }

    def plot_suggestions_analysis(self, x_sugg_all, pls_model, Y_des, methods=None, nearest_neighbors=3):
        """
        Analyze and visualize how each suggestion aligns with training data and predicted outcomes
        """
        # Plot suggestions with different markers and colors
        all_markers = ['o', 's', '^', 'D', 'v', '*', 'p', 'h', '8', '+', 'x', 'd']  # Add more if needed
        all_colors = ['red', 'blue', 'green', 'purple', 'orange', 'brown',
                      'magenta', 'cyan', 'lime', 'pink', 'teal', 'gold']  # Add more if needed

        markers = all_markers[:x_sugg_all.shape[0]]  # Take as many markers as needed
        colors = all_colors[:x_sugg_all.shape[0]]    # Take as many colors as needed

        fig = plt.subplots(2, 1, figsize=(12, 12))

        pca_x = pca().fit(self.X)

        pca_x = pca().fit(self.X, n_component=2) if pca_x.n_component < 2 else pca_x
        x_train_proj = pca_x.T
        x_sugg_proj = pca_x.evaluation(x_sugg_all).tscore
        geometric_metric = self.geometric_info(
            pds=x_sugg_all, latent_space=True, vicinity_neighbors=nearest_neighbors)
        ax2 = plt.subplot(2, 1, 1)
        ax2.scatter(x_train_proj[:, 0], x_train_proj[:, 1],
                    c='lightgray', alpha=0.5, label='Training Data')
        for i, (method, marker, color) in enumerate(zip(methods, markers, colors)):

            distances = np.sum((x_train_proj - x_sugg_proj[i])**2, axis=1)
            nearest_idx = np.argsort(distances)[:nearest_neighbors]
            mean_closest = geometric_metric[i, 0]
            mean_dist = geometric_metric[i, 1]

            ax2.scatter(x_sugg_proj[i, 0], x_sugg_proj[i, 1],
                        marker=marker, c=color, s=150, label=f'{method}(R2:{mean_closest:.2f}),(D:{mean_dist:.2f})')
            for idx in nearest_idx:
                ax2.plot([x_sugg_proj[i, 0], x_train_proj[idx, 0]],
                         [x_sugg_proj[i, 1], x_train_proj[idx, 1]],
                         c=color, alpha=0.2, linestyle='--')

        ax2.set_title(
            f'Latent Space Analysis (PCA Projection(n_com= {pca_x.n_component}))\nwith connections to {nearest_neighbors} nearest training points')
        ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax2.grid(True, alpha=0.3)

        y_pred = pls_model.predict(x_sugg_all)
        closeness = prediction_accuracy(target=Y_des, tried=y_pred, Y_org=self.Y, RorMse=1)

        ax3 = plt.subplot(2, 1, 2)
        bars = ax3.bar(methods, closeness, color=colors)
        ax3.set_title('clossness to Target')
        ax3.set_ylabel('PA')
        ax3.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar in bars:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height,
                     f'{height:.3f}',
                     ha='center', va='bottom')

        # Rotate x-labels for better readability
        plt.xticks(rotation=45)

        plt.tight_layout()
        plt.show()

    '''X only Not that much practical'''

    def plot_x_consitency(self, x_sugg, y_sugg_pre, x_sugg_labels, knn=5, knn_plot=True):

        def inner_plotter(x, y, x_sugg, y_sugg, y_des, y_des_range, labels_x_sugg, position):
            # Fit a line to the data
            markers = ['o', 's', '^', 'D', 'D', 'v', '*']
            colors = ['red', 'blue', 'green', 'purple', 'purple', 'orange', 'brown']
            slope_fit, intercept_fit = np.polyfit(x, y, 1)
            x_data = np.arange(x.min(), x.max(), .05)
            y_data = slope_fit * x_data + intercept_fit
            y_hat = slope_fit * x + intercept_fit

            # Calculate R²
            y_mean = np.mean(y)  # Mean of y
            SS_total = np.sum((y - y_mean)**2)  # Total sum of squares
            SS_residual = np.sum((y - y_hat)**2)  # Residual sum of squares
            R2 = 1 - (SS_residual / SS_total)  # R²

            # Create the subplot
            ax = plt.subplot(n_row, n_col, position)
            for j in range(x_sugg.shape[0]):
                scatter = ax.scatter(x_sugg[j], y_sugg[j], s=50, marker=markers[j],
                                     c=colors[j], label=labels_x_sugg[j])  # Use consistent vmin/vmax
            ax.scatter(x, y, c='lightgray', alpha=0.5, s=30)
            ax.plot(x_data, y_data, 'k-')

            # Add horizontal lines for y_des and y_des_range
            ax.axhline(y_des, color='magenta', linestyle='--', linewidth=1)
            ax.axhline(y_des-y_des_range, color='k', linestyle='--', linewidth=1, label='y_des range')
            ax.axhline(y_des+y_des_range, color='k', linestyle='--', linewidth=1, label='y_des range')

            ax.set_title(f'R2 = {R2:.2f}')

        X_all = self.X
        Y_all = self.Y

        y_des = self.Y_des.reshape(-1)
        y_des_range = self.Y_des_range.reshape(-1)

        X_all, Y_all = self.knn_update(y_des=y_des.reshape(1, -1), knn=knn) if knn_plot else (X_all, Y_all)

        x_var_label = self.xvarlabel
        y_var_label = self.yvarlabel

        n_col, n_row = X_all.shape[1], Y_all.shape[1]
        position = 1

        fig = plt.figure(figsize=(10, 8))  # Adjust figure size as needed

        # Plot each subplot
        for i in range(n_row):
            for j in range(n_col):
                inner_plotter(x=X_all[:, j], y=Y_all[:, i], x_sugg=x_sugg[:, j], y_sugg=y_sugg_pre[:, i],
                              labels_x_sugg=x_sugg_labels, position=position,
                              y_des=y_des[i], y_des_range=y_des_range[i])
                plt.xlabel(x_var_label[j]) if i == n_row-1 else None
                plt.ylabel(y_var_label[i]) if j == 0 else None
                plt.legend() if i == n_row-1 and j == n_col-1 else None
                position += 1

        plt.tight_layout()
        plt.show()

    '''  X and Y needed '''

    def iteration_assess_plot(self, y_tried, y_des=None, y_train=None):

        y_train = self.Y if y_train is None else y_train
        y_des = self.Y_des if y_des is None else y_des

        train_accuracy = prediction_accuracy(target=y_des, tried=y_train, Y_org=self.Y, RorMse=1)
        tried_accuracy = prediction_accuracy(target=y_des, tried=y_tried, Y_org=self.Y, RorMse=1)

        best_before = np.max(train_accuracy)
        best_now = np.max(tried_accuracy)

        n_train, n_tried = train_accuracy.size, tried_accuracy.size
        xpos = np.arange(n_train+n_tried)

        plt.figure(figsize=(10, 8))

        plt.bar(xpos[:n_train], train_accuracy, color='blue', label='Initial Data')
        plt.bar(xpos[n_train:], tried_accuracy, color='orange', label='Tried Data')
        plt.plot([0, n_train+n_tried+1], [best_now, best_now], '--',
                 color='orange', label=f'Best Tried Sample{best_now:.2f}')
        plt.plot([0, n_train+n_tried+1], [best_before, best_before], '--',
                 color='blue', label=f'Best Initial Sample{best_before:.2f}')

        plt.xticks(xpos, labels=['s '+str(i+1)for i in range(n_train)] +
                   ['t '+str(j+1) for j in range(n_tried)], rotation=90)
        plt.legend()
        plt.title(f'Iteration results of {self}')
        plt.ylim(bottom=0)
        plt.show()

    def output_space_plot(self, Y_train, y_itr=None, y_des=None, y_local=None):
        y_des = self.Y_des if y_des is None else y_des

        y_var_size = np.arange(Y_train.shape[1])
        y_var_size = np.concatenate((y_var_size, y_var_size[0].reshape(-1))) if y_var_size.size % 2 != 0 else y_var_size
        y_des = y_des.reshape(-1)

        n_col = int(y_var_size.size/2)
        x_axis_idx = 0
        y_axis_idx = 1

        plt.figure(figsize=(10, 8))
        plt.suptitle(f'output space of {self}')

        for k in range(n_col):
            var1 = y_var_size[x_axis_idx]
            var2 = y_var_size[y_axis_idx]
            plt.subplot(1, n_col, k+1)
            plt.scatter(Y_train[:, var1], Y_train[:, var2], s=75, marker='o', c='blue', label='Training samples')

            for i in range(Y_train.shape[0]):
                plt.text(Y_train[i, var1], Y_train[i, var2], f'{i}', fontdict={'fontsize': 12}, ha='left', va='top')

            if y_itr is not None:
                plt.scatter(y_itr[:, var1], y_itr[:, var2], s=100, marker='*', c='orange', label='Iterations samples')
                for i in range(y_itr.shape[0]):
                    plt.text(y_itr[i, var1], y_itr[i, var2], f'T{i+1}', fontdict={'fontsize': 12}, ha='right')

            plt.scatter(y_des[var1], y_des[var2], s=200, marker='*', c='purple')

            if y_local is not None:
                plt.scatter(y_local[:, var1], y_local[:, var2], s=100, marker='s', c='red', label='Knn')

            plt.xlabel(self.yvarlabel[var1])
            plt.ylabel(self.yvarlabel[var2])
            x_axis_idx += 2
            y_axis_idx += 2
        plt.tight_layout()
        plt.legend()
        plt.show()

    ''' General settings'''

    def archive(self, watch=True, saving=False, loading=False, key_version=None, file_name=''):
        archive_path = self.archive_path
        now = datetime.now()
        file_name = file_name+str(self)
        file_name = archive_path+file_name
        key_str = now.strftime("%B_%d_%H_%M").lower()

        if saving:
            add_new_to_database(filename=file_name, key=key_str, value=self)

        elif loading:
            file_dict = load_database(filename=file_name)
            self_archive: Problem = file_dict[key_version]
            return self_archive
        elif watch:
            dict_archive = load_database(filename=file_name)
            print(dict_archive)
            return dict_archive
