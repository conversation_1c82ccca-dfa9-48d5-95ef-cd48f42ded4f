import numpy as np
import pytest
from Projects.simulated_data.sim_data import SimData
from LVMI.inversion_methods.prep_model import PrepModel
from LVM.pls import PlsClass as pls
from LVM.pca import PcaClass as pca


@pytest.fixture
def sim_case():
    active_case = SimData(N=30, case_id=1)
    active_case.Y_des = SimData(N=1, case_id=1).Y
    return active_case


@pytest.fixture
def prep_instance(sim_case):
    return PrepModel(active_case=sim_case)


def test_prep_model_initialization(sim_case):
    """Test proper initialization of PrepModel with default parameters"""
    prep_model = PrepModel(active_case=sim_case)

    assert prep_model.active_case == sim_case
    assert np.array_equal(prep_model.X_org, sim_case.X)
    assert np.array_equal(prep_model.Y_org, sim_case.Y)
    assert prep_model.NplsCom == sim_case.X.shape[1]
    assert prep_model.NpcaCom == sim_case.X.shape[1] - 1
    assert prep_model.Knn == 5
    assert prep_model.itr == 0
    assert len(prep_model.itr_history) == 0


def test_prep_execution(sim_case):
    """Test full PREP execution workflow"""
    prep_model = PrepModel(active_case=sim_case)
    prep_model.execute()

    assert len(prep_model.itr_history) == 1
    current_itr = prep_model.itr_history[-1]

    # Check if models were created
    assert isinstance(current_itr.PLS_XY, pls)
    assert isinstance(current_itr.PCA_X, pca)

    # Check if PDS was generated
    assert current_itr.X_pds is not None
    assert current_itr.Y_pds is not None
    assert current_itr.pds_prep_score is not None


def test_knn_update(prep_instance):
    """Test K-nearest neighbors update functionality"""
    current_itr = prep_instance.ItreInfo(prep_model=prep_instance)
    prep_instance.Knn_update(current_itr)

    assert current_itr.X_train.shape[0] == prep_instance.Knn
    assert current_itr.Y_train.shape[0] == prep_instance.Knn


def test_model_alignment_matrix(prep_instance):
    """Test model alignment matrix calculation"""
    # First create necessary models
    current_itr = prep_instance.ItreInfo(prep_model=prep_instance)
    prep_instance.Knn_update(current_itr)

    pls_model = pls().fit(current_itr.X_train, current_itr.Y_train,
                          n_component=prep_instance.NplsCom)
    pca_model = pca().fit(current_itr.X_train, n_component=prep_instance.NpcaCom)

    alignment = prep_instance.model_alignment_matrix(pls_model, pca_model, current_itr.X_train)

    assert alignment.shape[1] == 6  # HT2, SPEx, h for both PLS and PCA
    assert alignment.shape[0] == current_itr.X_train.shape[0]


def test_prep_equation_optimization(prep_instance):
    """Test PREP equation optimization"""
    current_itr = prep_instance.ItreInfo(prep_model=prep_instance)
    prep_instance.Knn_update(current_itr)
    prep_instance.apply_prep(current_itr=current_itr)

    assert current_itr.prep_eq is not None
    assert current_itr.prep_cost is not None
    assert current_itr.prep_sol is not None
    assert isinstance(current_itr.prep_sol, dict)


def test_pds_update(prep_instance):
    """Test PDS (Prediction Design Space) update"""
    current_itr = prep_instance.ItreInfo(prep_model=prep_instance)
    prep_instance.Knn_update(current_itr)
    prep_instance.apply_prep(current_itr=current_itr)
    prep_instance.pds_update(current_itr=current_itr)

    assert current_itr.X_pds is not None
    assert current_itr.Y_pds is not None
    assert current_itr.X_pds.shape[1] == prep_instance.X_org.shape[1]
    assert current_itr.Y_pds.shape[1] == prep_instance.Y_org.shape[1]


def test_itr_exp_update(prep_instance):
    """Test iteration experimental update"""
    # Execute PREP first
    prep_instance.execute()

    # Generate some experimental data
    current_itr = prep_instance.itr_history[-1]
    x_exp = current_itr.xitr_
    y_exp = prep_instance.active_case.y_calculator(x_exp)

    prep_instance.complete_current_itr(y_exp_current_itr=y_exp)

    assert prep_instance.itr == 1
    assert np.array_equal(prep_instance.y_itr, y_exp)
    assert np.array_equal(prep_instance.x_itr, x_exp)


def test_different_case_studies():
    """Test PREP with different case studies"""
    cases = [
        SimData(N=30, case_id=1),
        SimData(N=40, case_id=1)
    ]

    for case in cases:
        case.Y_des = SimData(N=1, case_id=1).Y
        prep_model = PrepModel(active_case=case)
        prep_model.execute()

        assert len(prep_model.itr_history) == 1
        assert prep_model.itr_history[-1].X_pds is not None


def test_prep_with_custom_parameters(sim_case):
    """Test PREP with custom initialization parameters"""
    prep_model = PrepModel(
        active_case=sim_case,
        Npls_component=2,
        Npca_component=2,
        Knn=8,
        minNsolution=15,
        NegativeCoeff=True,
        MI=2,
        alfa_lvm=0.90,
        RorMse=2
    )
    prep_model.execute()

    assert prep_model.NplsCom == 2
    assert prep_model.NpcaCom == 2
    assert prep_model.Knn == 8
    assert prep_model.minNsolution == 15
    assert prep_model.NegativeCoeff is True
    assert prep_model.MI == 2
    assert prep_model.alfa_lvm == 0.90
    assert prep_model.RorMse == 2
