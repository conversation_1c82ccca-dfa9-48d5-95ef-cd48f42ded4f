import numpy as np
import pytest
from Projects.simulated_data.sim_data import Sim<PERSON><PERSON>
from LVMI.inversion_methods.prep_model import PrepModel

# Base class tests


def test_base_class_inheritance():
    """Test SimData inherits correctly from Problem base class"""
    case = SimData(N=30, case_id=1)
    assert hasattr(case, 'X')
    assert hasattr(case, 'Y')
    assert hasattr(case, 'Y_des')
    assert hasattr(case, 'Y_des_range')
    assert hasattr(case, 'Xmin')
    assert hasattr(case, 'Xmax')
    assert hasattr(case, 'xvarlabel')
    assert hasattr(case, 'yvarlabel')
    assert hasattr(case, 'case_id')


def test_applying_conditions_X():
    """Test the base class applying_conditions_X method"""
    case = SimData(N=30, case_id=1)
    x_data = np.random.uniform(case.Xmin - 1, case.Xmax + 1, size=(50, case.X.shape[1]))
    y_data = np.random.uniform(-1, 1, size=(50, case.Y.shape[1]))

    filtered_x, filtered_y = case.applying_conditions_X(x_data, y_data)

    # Check if filtered data is within bounds
    assert np.all(filtered_x >= case.Xmin)
    assert np.all(filtered_x <= case.Xmax)
    assert np.all(filtered_y >= 0)  # Assuming negative Y values should be filtered
    assert filtered_x.shape[0] == filtered_y.shape[0]


def test_nearest_neighbors_update():
    """Test the base class nearest_neighbors_update method"""
    case = SimData(N=30, case_id=1)
    target_point = np.mean(case.Y, axis=0).reshape(1, -1)

    sorted_idx = case.nearest_neighbors_update(
        data=case.Y,
        target_point=target_point,
        scalled=True,
        metric='euclidean',
        knn=5,
        visualplot=False
    )

    assert sorted_idx is not None
    assert len(sorted_idx) == case.Y.shape[0]
    assert len(np.unique(sorted_idx)) == len(sorted_idx)  # All indices should be unique


def test_iteration_assess_plot():
    """Test the base class iteration_assess_plot method"""
    case = SimData(N=30, case_id=1)
    case.Y_des = SimData(N=1, case_id=1).Y
    y_tried = np.random.uniform(size=(5, case.Y.shape[1]))

    # Should not raise any exceptions
    case.iteration_assess_plot(y_tried=y_tried)


def test_archive_functionality():
    """Test the base class archive functionality"""
    case = SimData(N=30, case_id=1)
    test_filename = "test_sim_archive"

    # Test saving
    case.archive(watch=False, s=True, l=False, file_name=test_filename)

    # Test watching (should return the dictionary of all archived cases)
    archived_dict = case.archive(watch=True, s=False, l=False, file_name=test_filename)
    assert isinstance(archived_dict, dict)
    assert len(archived_dict) > 0

    # Get the most recent key from the archived dictionary
    latest_key = list(archived_dict.keys())[-1]

    # Test loading using the actual key
    loaded_case = case.archive(watch=False, s=False, l=True,
                               key_version=latest_key, file_name=test_filename)

    assert isinstance(loaded_case, SimData)


@pytest.fixture
def sim_case():
    """Create a simulation case study instance"""
    case = SimData(N=30, case_id=1)
    case.Y_des = SimData(N=1, case_id=1).Y
    return case


def test_sim_data_initialization():
    """Test SimData case study initialization"""
    case = SimData(N=30, case_id=1)
    assert case.X.shape == (30, 3)
    assert case.Y.shape == (30, 2)
    assert hasattr(case, 'y_calculator')


def test_sim_data_full_iteration_cycle(sim_case):
    """Test complete iteration cycle with SimData"""
    prep_model = PrepModel(active_case=sim_case)

    # First iteration
    prep_model.execute()
    assert len(prep_model.itr_history) == 1

    # Get experimental Y using y_calculator
    x_exp = prep_model.itr_history[-1].xitr_
    y_exp = sim_case.y_calculator(x_exp)

    # Update with experimental results
    prep_model.complete_current_itr(y_exp_current_itr=y_exp)
    assert prep_model.itr == 1
    assert np.array_equal(prep_model.y_itr, y_exp)

    # Second iteration
    prep_model.execute()
    assert len(prep_model.itr_history) == 2


def test_sim_data_different_sample_sizes():
    """Test SimData with various sample sizes"""
    sample_sizes = [20, 30, 40]
    for N in sample_sizes:
        case = SimData(N=N, case_id=1)
        case.Y_des = SimData(N=1, case_id=1).Y

        prep_model = PrepModel(active_case=case)
        prep_model.execute()

        assert case.X.shape[0] == N
        assert case.Y.shape[0] == N


def test_sim_data_convergence():
    """Test convergence of SimData case study"""
    case = SimData(N=30, case_id=1)
    case.Y_des = SimData(N=1, case_id=1).Y
    prep_model = PrepModel(active_case=case)

    max_iterations = 5
    y_history = []

    for _ in range(max_iterations):
        prep_model.execute()
        x_exp = prep_model.itr_history[-1].xitr_
        y_exp = case.y_calculator(x_exp)
        y_history.append(y_exp)
        prep_model.complete_current_itr(y_exp_current_itr=y_exp)

    # Check if predictions are improving
    initial_error = np.mean((y_history[0] - case.Y_des)**2)
    final_error = np.mean((y_history[-1] - case.Y_des)**2)
    assert final_error <= initial_error
