import numpy as np
from numpy.testing import assert_array_almost_equal
import pytest
from Projects.simulated_data.sim_data import SimData


def test_pds_dist_accuracy_ranking():
    # Create a simple SimData instance with controlled test data
    sim_data = SimData()

    # Setup test data
    sim_data.X = np.array([
        [1, 1],  # Sample 1
        [2, 2],  # Sample 2
        [3, 3],  # Sample 3
        [4, 4],  # Sample 4
        [5, 5]   # Sample 5
    ])

    sim_data.Y = np.array([
        [10, 10],  # Output 1
        [20, 20],  # Output 2
        [30, 30],  # Output 3
        [40, 40],  # Output 4
        [50, 50]   # Output 5
    ])

    # Create mock PrepModel with controlled PDS points
    class MockPrepModel:
        def __init__(self):
            self.itr_history = [type('Itr', (), {
                'X_pds': np.array([
                    [1.5, 1.5],  # PDS 1 - close to Sample 1
                    [4.5, 4.5],  # PDS 2 - close to Sample 4
                    [3.0, 3.0]   # PDS 3 - exactly on Sample 3
                ]),
                'pds_prep_score': np.array([1.0, 1.0, 1.0])
            })]

    # Set target Y (desired output)
    sim_data.Y_des = np.array([[15, 15]])  # Closer to Output 1

    # Test the function
    result = sim_data.pds_dist_accuracy_ranking(
        prep_model=MockPrepModel(),
        knn=2,
        prep_inclusion_in_ranking=False
    )

    # Assertions

    # 1. Check if result is a single point
    assert result.shape == (2,), "Result should be a single 2D point"

    # 2. Check if the function selected the best PDS point
    # In this case, PDS 1 should be selected as it's closest to samples
    # that have outputs near the desired output
    assert_array_almost_equal(result, [1.5, 1.5], decimal=2)

    # 3. Test with different knn value
    result_knn3 = sim_data.pds_dist_accuracy_ranking(
        prep_model=MockPrepModel(),
        knn=3,
        prep_inclusion_in_ranking=False
    )
    assert result_knn3.shape == (2,), "Result should be a single 2D point"

    # 4. Test with prep_inclusion_in_ranking=True
    result_with_prep = sim_data.pds_dist_accuracy_ranking(
        prep_model=MockPrepModel(),
        knn=2,
        prep_inclusion_in_ranking=True
    )
    assert result_with_prep.shape == (2,), "Result should be a single 2D point"

    # 5. Test edge cases
    # Test with single neighbor
    result_knn1 = sim_data.pds_dist_accuracy_ranking(
        prep_model=MockPrepModel(),
        knn=1,
        prep_inclusion_in_ranking=False
    )
    assert result_knn1.shape == (2,), "Result should work with knn=1"

    # Test with all neighbors
    result_knn_all = sim_data.pds_dist_accuracy_ranking(
        prep_model=MockPrepModel(),
        knn=5,
        prep_inclusion_in_ranking=False
    )
    assert result_knn_all.shape == (2,), "Result should work with knn=number of samples"

    print("All tests passed!")

    # Additional verification prints for debugging
    print("\nDetailed verification:")
    print(f"Selected PDS point: {result}")
    print(f"Distance to training samples:")
    for i, x in enumerate(sim_data.X):
        dist = np.linalg.norm(result - x)
        print(f"Distance to sample {i+1}: {dist:.2f}")
    print(f"\nCorresponding Y values of nearest neighbors:")
    distances = np.array([np.linalg.norm(result - x) for x in sim_data.X])
    nearest_idx = np.argsort(distances)[:2]  # Get 2 nearest neighbors
    print(f"Y values of nearest neighbors: \n{sim_data.Y[nearest_idx]}")
    print(f"Desired Y: {sim_data.Y_des}")
