import pickle
import os


def init_database(filename: str):
    filename = (filename + '.pkl') if not filename.endswith('.pkl') else filename
    create_permission = "Y"
    if os.path.exists(filename):
        create_permission = input('File already exist. Do you want to override? (Y/N)')

    if create_permission == "Y":
        with open(filename, "wb") as file:
            pickle.dump({}, file)
    else:
        pass


def save_database(data, filename):
    filename = (filename + '.pkl') if not filename.endswith('.pkl') else filename
    with open(filename, "wb") as file:
        pickle.dump(data, file)


def load_database(filename):
    filename = (filename + '.pkl') if not filename.endswith('.pkl') else filename
    if not os.path.exists(filename):
        init_database(filename=filename)

    with open(filename, "rb") as file:
        data = pickle.load(file)
    return data


def add_new_to_database(key, value, filename):
    filename = (filename + '.pkl') if not filename.endswith('.pkl') else filename
    data = load_database(filename=filename)
    data[key] = value
    save_database(data, filename)
