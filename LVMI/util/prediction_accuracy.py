import numpy as np


def prediction_accuracy(target: np.ndar<PERSON>, tried: np.ndarray, Y_org: np.ndarray = None, RorMse=1):
    '''Prediction Accuracy analyzer Based on:
        R : the higher the better 
        MSE_like : The lower the better '''

    Y_org = target if Y_org is None else Y_org

    DataRange = (np.maximum(np.max(target, axis=0), np.percentile(Y_org, 95, axis=0))) - \
        (np.minimum(np.percentile(target, 5, axis=0), np.min(Y_org, axis=0)))
    diff = np.abs(target-tried)

    if RorMse == 1:
        pre_accuracy = 1-np.max(diff/DataRange, axis=1)
    elif <PERSON>e == 2:
        pre_accuracy = np.max(diff/DataRange, axis=1)
    elif <PERSON>or<PERSON>e == 3:  # One Option from the past
        c = np.mean(Y_org)
        s = np.std(Y_org, ddof=1)
        Yact_scaled = (target-c) / s
        Ypre_caled = (tried-c) / s

        diff = np.sum(np.abs(Yact_scaled-Ypre_caled), axis=1)
        pre_accuracy = 1-diff
    elif <PERSON>or<PERSON> == 4:  # bing the one in the manuscript
        pass
    return pre_accuracy
