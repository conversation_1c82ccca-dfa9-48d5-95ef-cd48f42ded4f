<mxfile host="65bd71144e">
    <diagram id="5pNX2USA4fDTeIJD3oNJ" name="Page-1">
        <mxGraphModel dx="1090" dy="630" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="" style="rhombus;whiteSpace=wrap;html=1;strokeWidth=2;fillWeight=-1;hachureGap=8;fillStyle=cross-hatch;fillColor=#006600;sketch=1;" parent="1" vertex="1">
                    <mxGeometry x="220" y="250" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="" style="shape=table;html=1;whiteSpace=wrap;startSize=0;container=1;collapsible=0;childLayout=tableLayout;" parent="1" vertex="1">
                    <mxGeometry x="190" y="220" width="180" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="3" vertex="1">
                    <mxGeometry width="180" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="4" vertex="1">
                    <mxGeometry width="60" height="40" as="geometry">
                        <mxRectangle width="60" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="6" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="4" vertex="1">
                    <mxGeometry x="60" width="60" height="40" as="geometry">
                        <mxRectangle width="60" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="7" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="4" vertex="1">
                    <mxGeometry x="120" width="60" height="40" as="geometry">
                        <mxRectangle width="60" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="8" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="3" vertex="1">
                    <mxGeometry y="40" width="180" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="8" vertex="1">
                    <mxGeometry width="60" height="40" as="geometry">
                        <mxRectangle width="60" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="8" vertex="1">
                    <mxGeometry x="60" width="60" height="40" as="geometry">
                        <mxRectangle width="60" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="8" vertex="1">
                    <mxGeometry x="120" width="60" height="40" as="geometry">
                        <mxRectangle width="60" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="3" vertex="1">
                    <mxGeometry y="80" width="180" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="12" vertex="1">
                    <mxGeometry width="60" height="40" as="geometry">
                        <mxRectangle width="60" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="12" vertex="1">
                    <mxGeometry x="60" width="60" height="40" as="geometry">
                        <mxRectangle width="60" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="15" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;pointerEvents=1;" parent="12" vertex="1">
                    <mxGeometry x="120" width="60" height="40" as="geometry">
                        <mxRectangle width="60" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="" style="shape=table;html=1;whiteSpace=wrap;startSize=0;container=1;collapsible=0;childLayout=tableLayout;fillColor=#6a00ff;fontColor=#ffffff;strokeColor=#3700CC;" parent="1" vertex="1">
                    <mxGeometry x="140" y="170" width="230" height="210" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="16" vertex="1">
                    <mxGeometry width="230" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="17" vertex="1">
                    <mxGeometry width="77" height="40" as="geometry">
                        <mxRectangle width="77" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="17" vertex="1">
                    <mxGeometry x="77" width="76" height="40" as="geometry">
                        <mxRectangle width="76" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="20" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="17" vertex="1">
                    <mxGeometry x="153" width="77" height="40" as="geometry">
                        <mxRectangle width="77" height="40" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="16" vertex="1">
                    <mxGeometry y="40" width="230" height="56" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="21" vertex="1">
                    <mxGeometry width="77" height="56" as="geometry">
                        <mxRectangle width="77" height="56" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="23" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="21" vertex="1">
                    <mxGeometry x="77" width="76" height="56" as="geometry">
                        <mxRectangle width="76" height="56" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="24" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="21" vertex="1">
                    <mxGeometry x="153" width="77" height="56" as="geometry">
                        <mxRectangle width="77" height="56" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="25" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="16" vertex="1">
                    <mxGeometry y="96" width="230" height="57" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="25" vertex="1">
                    <mxGeometry width="77" height="57" as="geometry">
                        <mxRectangle width="77" height="57" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="27" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="25" vertex="1">
                    <mxGeometry x="77" width="76" height="57" as="geometry">
                        <mxRectangle width="76" height="57" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="28" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;pointerEvents=1;" parent="25" vertex="1">
                    <mxGeometry x="153" width="77" height="57" as="geometry">
                        <mxRectangle width="77" height="57" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="33" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="16" vertex="1">
                    <mxGeometry y="153" width="230" height="57" as="geometry"/>
                </mxCell>
                <mxCell id="34" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="33" vertex="1">
                    <mxGeometry width="77" height="57" as="geometry">
                        <mxRectangle width="77" height="57" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="35" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;" parent="33" vertex="1">
                    <mxGeometry x="77" width="76" height="57" as="geometry">
                        <mxRectangle width="76" height="57" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="36" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;overflow=hidden;pointerEvents=1;" parent="33" vertex="1">
                    <mxGeometry x="153" width="77" height="57" as="geometry">
                        <mxRectangle width="77" height="57" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="29" value="&lt;ul&gt;&lt;li&gt;Value 1&lt;/li&gt;&lt;li&gt;Value 2&lt;/li&gt;&lt;li&gt;Value 3&lt;/li&gt;&lt;/ul&gt;" style="text;strokeColor=none;fillColor=none;html=1;whiteSpace=wrap;verticalAlign=middle;overflow=hidden;" parent="1" vertex="1">
                    <mxGeometry x="120" y="60" width="100" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="250" y="50" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="38" value="&lt;h1&gt;nnnn&lt;/h1&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" parent="1" vertex="1">
                    <mxGeometry x="240" y="240" width="80" height="80" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>