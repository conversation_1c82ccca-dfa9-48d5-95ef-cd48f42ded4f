"""
Multi-Block Partial Least Squares (MB-PLS) Implementation

This module provides a multi-block PLS implementation that inherits from the existing PlsClass
and extends it to handle multiple blocks of X data while maintaining compatibility.
"""

import numpy as np
from typing import List, Optional
from scipy.stats import chi2
from .pls import PlsClass, plseval


class MBPLSeval:
    """Evaluation results for Multi-Block PLS"""

    def __init__(self, yfit=None, tscore=None, t_blocks=None, HT2=None,
                 spe_blocks=None, spe_global=None, xhat_blocks=None, hmbpls=None) -> None:
        self.yfit = yfit
        self.tscore = tscore  # Super scores (ts)
        self.t_blocks = t_blocks  # Block scores (tk for each block)
        self.HT2 = HT2
        self.spe_blocks = spe_blocks  # SPE for each block
        self.spe_global = spe_global  # Global SPE across all blocks
        self.xhat_blocks = xhat_blocks  # Reconstructed X for each block
        self.hmbpls = hmbpls


class MultiBlockPLS(PlsClass):
    """
    Multi-Block Partial Least Squares (MB-PLS) implementation that inherits from PlsClass.

    This class extends the standard PLS to handle multiple blocks of X data while maintaining
    compatibility with the existing PLS interface and methods.

    Key differences from standard PLS:
    - Input: List of X matrices instead of single X matrix
    - Scaling: Independent scaling for each block
    - Scores: Block scores (t_k) + Super scores (t_s)
    - SPE: Block-specific SPE + Global SPE
    - Weights: Block importance weights (A and A_corrected)
    """

    def __init__(self) -> None:
        super().__init__()
        # Multi-block specific attributes
        self.X_blocks = None
        self.n_blocks = None
        self.block_weights = None  # A matrix (block importances)
        self.block_weights_corrected = None  # A_corrected matrix
        self.W_blocks = None  # Weight matrices for each block
        self.P_blocks = None  # Loading matrices for each block
        self.T_blocks = None  # Score matrices for each block
        self.block_scaling = None  # Scaling info for each block
        self.block_sizes = None  # Size of each block
        self.R_global = None  # Global rotation matrix
        self.beta_global = None  # Global regression coefficients

    def fit(self, X_blocks: List[np.ndarray], Y: np.ndarray, n_component: int = None,
            alpha: float = 0.95, to_be_scaled: bool = True):
        """
        Fit Multi-Block PLS model using NIPALS algorithm adapted for multiple blocks.

        Args:
            X_blocks (List[np.ndarray]): List of X data blocks
            Y (np.ndarray): Y data matrix
            n_component (int, optional): Number of PLS components. Defaults to None.
            alpha (float, optional): Modelling confidence limit. Defaults to 0.95.
            to_be_scaled (bool, optional): Whether to scale the data. Defaults to True.

        Returns:
            self: Fitted MultiBlockPLS model
        """
        # Store original blocks
        self.X_blocks = X_blocks
        self.n_blocks = len(X_blocks)
        self.block_sizes = [block.shape[1] for block in X_blocks]

        # Concatenate blocks for initial processing (similar to standard PLS)
        X_concat = np.concatenate(X_blocks, axis=1)

        # Use parent class preprocessing
        X_concat, Y, removed_row, removed_col = self._preprocessing(X_concat, Y)

        # Update X_blocks after preprocessing
        if removed_row.size > 0:
            X_blocks = [np.delete(block, removed_row, axis=0) for block in X_blocks]

        X_orining = X_concat
        Y_orining = Y

        # Multi-block scaling
        if to_be_scaled:
            X_blocks_scaled, Y_scaled, block_scaling, y_scaling = self._scale_blocks(X_blocks, Y)
            self.block_scaling = block_scaling
            self.y_scaling = np.vstack((y_scaling[0], y_scaling[1]))
        else:
            X_blocks_scaled = X_blocks
            Y_scaled = Y
            self.block_scaling = None
            self.y_scaling = None

        # Determine number of components
        n_component = self.set_n_component(X_concat) if n_component is None else n_component
        n_component = np.min((n_component, X_concat.shape[1], X_concat.shape[0]-1))

        if n_component < 1:
            raise ValueError("data does not have any variance")

        self.n_component = n_component
        self.alpha = alpha

        # Initialize matrices for multi-block NIPALS
        Num_obs = X_concat.shape[0]

        # Initialize block-specific matrices
        self.W_blocks = [np.zeros((block.shape[1], n_component)) for block in X_blocks_scaled]
        self.P_blocks = [np.zeros((block.shape[1], n_component)) for block in X_blocks_scaled]
        self.T_blocks = [np.zeros((Num_obs, n_component)) for _ in X_blocks_scaled]

        # Initialize global matrices
        self.block_weights = np.zeros((self.n_blocks, n_component))
        T_super = np.zeros((Num_obs, n_component))
        U = np.zeros((Num_obs, n_component))
        Q = np.zeros((Y_scaled.shape[1], n_component))

        # Working copies
        X_work = [block.copy() for block in X_blocks_scaled]
        Y_work = Y_scaled.copy()

        # Multi-Block NIPALS Algorithm
        for comp in range(n_component):
            # Initialize u with the Y column with maximum variance
            u = Y_work[:, np.argmax(np.var(Y_orining, axis=0, ddof=1))]

            while True:
                # Calculate block weights and scores for each block
                w_blocks = []
                t_blocks = []

                for i, X_block in enumerate(X_work):
                    # Calculate weight vector for this block
                    w_k = X_block.T @ u / (u.T @ u)
                    w_k = w_k / np.linalg.norm(w_k)
                    w_blocks.append(w_k)

                    # Calculate block score
                    t_k = X_block @ w_k
                    t_blocks.append(t_k)

                # Calculate block importances (A matrix)
                block_importances = np.zeros(self.n_blocks)
                for i, t_k in enumerate(t_blocks):
                    block_importances[i] = (u.T @ t_k) / (t_k.T @ t_k)
                block_importances = block_importances/np.sum(block_importances)

                # Calculate super scores (ts)
                t_super = np.zeros_like(t_blocks[0])
                for i, t_k in enumerate(t_blocks):
                    t_super += block_importances[i] * t_k

                # Calculate Y loadings
                q = Y_work.T @ t_super / (t_super.T @ t_super)

                # Update u
                u_new = Y_work @ q / (q.T @ q)

                # Check convergence
                print(f'block importance = {block_importances}')
                if np.allclose(u, u_new, atol=1e-6):
                    break
                u = u_new

            # Store results for this component
            for i in range(self.n_blocks):
                self.W_blocks[i][:, comp] = w_blocks[i]
                self.T_blocks[i][:, comp] = t_blocks[i]

            self.block_weights[:, comp] = block_importances
            T_super[:, comp] = t_super
            U[:, comp] = u_new
            Q[:, comp] = q

            # Calculate loadings for each block
            for i, X_block in enumerate(X_work):
                p_k = X_block.T @ t_blocks[i] / (t_blocks[i].T @ t_blocks[i])
                self.P_blocks[i][:, comp] = p_k

                # Deflate X block
                X_work[i] = X_work[i] - t_blocks[i][:, None] @ p_k[None, :]

            # Deflate Y
            Y_work = Y_work - t_super[:, None] @ q[None, :]

        # Calculate corrected block weights (A_corrected)
        total_vars = sum(self.block_sizes)
        self.block_weights_corrected = np.zeros_like(self.block_weights)
        for i in range(self.n_blocks):
            correction_factor = 1 - (self.block_sizes[i] / total_vars)
            self.block_weights_corrected[i, :] = self.block_weights[i, :] * correction_factor

        # Store standard PLS attributes for compatibility
        self.T = T_super
        self.U = U
        self.Q = Q

        # Calculate global rotation matrix (R) and regression coefficients
        self._calculate_global_matrices()

        # Calculate statistics (SPE, T2, etc.)
        self._calculate_statistics(X_blocks_scaled, Y_scaled, alpha)

        return self

    def _preprocessing(self, X, Y):
        """Preprocessing function extracted from parent class"""
        # col with no variance
        X_col_zero_var = np.where(X.var(axis=0) < 1e-8)[0]
        X = np.delete(X, X_col_zero_var, axis=1) if X_col_zero_var.size > 0 else X

        removed_col = np.where(Y.var(axis=0) < 1e-8)[0]
        Y = np.delete(Y, removed_col, axis=1) if removed_col.size > 0 else Y

        # row with no variance
        removed_row = np.union1d(np.where(X.var(axis=1) < 1e-8)[0], np.where(
            Y.var(axis=1) < 1e-8)[0]) if Y.shape[1] > 1 else np.where(X.var(axis=1) < 1e-8)[0]
        if removed_row.size > 0:
            X = np.delete(X, removed_row, axis=0)
            Y = np.delete(Y, removed_row, axis=0)

        return X, Y, removed_row, removed_col

    def _scale_blocks(self, X_blocks: List[np.ndarray], Y: np.ndarray):
        """Scale each block independently"""
        X_blocks_scaled = []
        block_scaling = []

        for block in X_blocks:
            Cx = np.mean(block, axis=0)
            Sx = np.std(block, axis=0, ddof=1) + 1e-16
            block_scaled = (block - Cx) / Sx
            X_blocks_scaled.append(block_scaled)
            block_scaling.append(np.vstack((Cx, Sx)))

        # Scale Y
        Cy = np.mean(Y, axis=0)
        Sy = np.std(Y, axis=0, ddof=1) + 1e-16
        Y_scaled = (Y - Cy) / Sy
        y_scaling = (Cy, Sy)

        return X_blocks_scaled, Y_scaled, block_scaling, y_scaling

    def _calculate_global_matrices(self):
        """Calculate global rotation matrix R and regression coefficients"""
        # Concatenate block weights and loadings
        W_concat = np.vstack([W for W in self.W_blocks])
        P_concat = np.vstack([P for P in self.P_blocks])

        # Calculate global rotation matrix
        self.R_global = W_concat @ np.linalg.pinv(P_concat.T @ W_concat)

        # Calculate global regression coefficients
        self.beta_global = self.R_global @ self.Q.T

        # Store for compatibility with parent class
        self.Wstar = self.R_global
        self.B_pls = self.beta_global

    def _calculate_statistics(self, X_blocks_scaled: List[np.ndarray], Y_scaled: np.ndarray, alpha: float):
        """Calculate SPE, T2, and other statistics for multi-block PLS"""
        n_obs = X_blocks_scaled[0].shape[0]

        # Initialize statistics arrays
        self.SPE_x = np.zeros((n_obs, self.n_component))
        self.SPE_lim_x = np.zeros(self.n_component)
        self.SPE_y = np.zeros((n_obs, self.n_component))
        self.SPE_lim_y = np.zeros(self.n_component)
        self.tsquared = np.zeros((n_obs, self.n_component))
        self.T2_lim = np.zeros(self.n_component)
        self.ellipse_radius = np.zeros(self.n_component)

        # Calculate statistics for each component
        for comp in range(self.n_component):
            # SPE for X (global across all blocks)
            X_concat = np.concatenate(X_blocks_scaled, axis=1)
            P_concat = np.vstack([P[:, :comp+1] for P in self.P_blocks])

            self.SPE_x[:, comp], self.SPE_lim_x[comp], _ = self.SPE_calculation(
                self.T[:, :comp+1], P_concat, X_concat, alpha)

            # SPE for Y
            self.SPE_y[:, comp], self.SPE_lim_y[comp], _ = self.SPE_calculation(
                self.T[:, :comp+1], self.Q[:, :comp+1], Y_scaled, alpha)

            # Hotelling T2
            self.tsquared[:, comp], self.T2_lim[comp], self.ellipse_radius[comp] = self.T2_calculations(
                self.T[:, :comp+1], comp+1, n_obs, alpha)

    def scaler(self, X_blocks_new: List[np.ndarray] = None, Y_new: np.ndarray = None):
        """Scale new data using stored scaling parameters for each block"""
        X_blocks_scaled, Y_new_scaled = None, None

        if X_blocks_new is not None and self.block_scaling is not None:
            X_blocks_scaled = []
            for i, block in enumerate(X_blocks_new):
                Cx = self.block_scaling[i][0, :]
                Sx = self.block_scaling[i][1, :]
                block_scaled = (block - Cx) / Sx
                X_blocks_scaled.append(block_scaled)
        elif X_blocks_new is not None:
            X_blocks_scaled = X_blocks_new

        if Y_new is not None and self.y_scaling is not None:
            Cy = self.y_scaling[0, :]
            Sy = self.y_scaling[1, :]
            Y_new_scaled = (Y_new - Cy) / Sy
        elif Y_new is not None:
            Y_new_scaled = Y_new

        return X_blocks_scaled, Y_new_scaled

    def unscaler(self, X_blocks_new: List[np.ndarray] = None, Y_new: np.ndarray = None):
        """Unscale data using stored scaling parameters for each block"""
        X_blocks_unscaled, Y_new_unscaled = None, None

        if X_blocks_new is not None and self.block_scaling is not None:
            X_blocks_unscaled = []
            for i, block in enumerate(X_blocks_new):
                Cx = self.block_scaling[i][0, :]
                Sx = self.block_scaling[i][1, :]
                block_unscaled = (block * Sx) + Cx
                X_blocks_unscaled.append(block_unscaled)
        elif X_blocks_new is not None:
            X_blocks_unscaled = X_blocks_new

        if Y_new is not None and self.y_scaling is not None:
            Cy = self.y_scaling[0, :]
            Sy = self.y_scaling[1, :]
            Y_new_unscaled = (Y_new * Sy) + Cy
        elif Y_new is not None:
            Y_new_unscaled = Y_new

        return X_blocks_unscaled, Y_new_unscaled

    def predict(self, X_blocks_new: List[np.ndarray]) -> np.ndarray:
        """Predict Y for new multi-block X data"""
        X_blocks_scaled, _ = self.scaler(X_blocks_new=X_blocks_new)

        # Concatenate scaled blocks
        X_concat_scaled = np.concatenate(X_blocks_scaled, axis=1)

        # Predict using global regression coefficients
        y_fit_scaled = X_concat_scaled @ self.beta_global

        # Unscale prediction
        _, y_fit = self.unscaler(Y_new=y_fit_scaled)

        return y_fit

    def evaluation(self, X_blocks_test: List[np.ndarray]) -> MBPLSeval:
        """
        Evaluate multi-block PLS model on new data

        Args:
            X_blocks_test: List of test data blocks

        Returns:
            MBPLSeval: Multi-block evaluation results
        """
        # Predict Y
        yfit = self.predict(X_blocks_test)

        # Scale test data
        X_blocks_scaled, _ = self.scaler(X_blocks_new=X_blocks_test)

        # Calculate block scores (t_k for each block)
        t_blocks = []
        for i, X_block in enumerate(X_blocks_scaled):
            t_k = X_block @ self.W_blocks[i]
            t_blocks.append(t_k)

        # Calculate super scores (t_s)
        tscore = np.zeros_like(t_blocks[0])
        for i, t_k in enumerate(t_blocks):
            tscore += self.block_weights_corrected[i, :] * t_k

        # Calculate Hotelling T2
        HT2 = np.sum((tscore / np.std(self.T, axis=0, ddof=1))**2, axis=1)

        # Calculate SPE for each block
        spe_blocks = []
        xhat_blocks = []
        for i, (X_block, t_k) in enumerate(zip(X_blocks_scaled, t_blocks)):
            # Reconstruct X for this block
            xhat_block = t_k @ self.P_blocks[i].T
            xhat_blocks.append(xhat_block)

            # Calculate SPE for this block
            error_block = X_block - xhat_block
            spe_block = np.sum(error_block**2, axis=1)
            spe_blocks.append(spe_block)

        # Calculate global SPE across all blocks
        X_concat = np.concatenate(X_blocks_scaled, axis=1)
        xhat_concat = np.concatenate(xhat_blocks, axis=1)
        error_global = X_concat - xhat_concat
        spe_global = np.sum(error_global**2, axis=1)

        return MBPLSeval(
            yfit=yfit,
            tscore=tscore,
            t_blocks=t_blocks,
            HT2=HT2,
            spe_blocks=spe_blocks,
            spe_global=spe_global,
            xhat_blocks=xhat_blocks,
            hmbpls=self
        )

    def calculate_block_spe_limits(self, alpha: float = 0.95) -> List[float]:
        """Calculate SPE limits for each block separately"""
        spe_limits = []

        for i in range(self.n_blocks):
            # Get training block scores and loadings
            T_block = self.T_blocks[i]
            P_block = self.P_blocks[i]
            X_block = self.X_blocks[i] if hasattr(self, 'X_blocks') else None

            if X_block is not None:
                # Calculate SPE for training data
                xhat_block = T_block @ P_block.T
                error_block = X_block - xhat_block
                spe_block = np.sum(error_block**2, axis=1)

                # Calculate SPE limit
                m = np.mean(spe_block)
                v = np.var(spe_block, ddof=1)
                spe_limit = v / (2 * m) * chi2.ppf(alpha, 2 * m**2 / (v + 1e-15))
                spe_limits.append(spe_limit)
            else:
                spe_limits.append(None)

        return spe_limits
