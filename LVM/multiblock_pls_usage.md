# MultiBlockPLS Usage Guide

## Overview

The `MultiBlockPLS` class is a multi-block partial least squares implementation that inherits from your existing `PlsClass`. It extends the standard PLS to handle multiple blocks of X data while maintaining compatibility with the existing PLS interface and methods.

## Key Features

- **Inherits from PlsClass**: All standard PLS methods are available
- **Multi-block support**: Handles multiple X data blocks with different sizes
- **Block-specific scaling**: Each block is scaled independently
- **Block importance weights**: Calculates A and A_corrected matrices
- **Block-specific SPE**: Calculates SPE for each block separately plus global SPE
- **Compatible interface**: Same API as standard PLS for easy migration

## Key Differences from Standard PLS

### Input Data Format
- **Standard PLS**: Single X matrix `(n_samples, n_features)`
- **MultiBlock PLS**: List of X matrices `[X1, X2, ..., Xk]` where each `Xi` is `(n_samples, n_features_i)`

### Scaling
- **Standard PLS**: Global scaling across all features
- **MultiBlock PLS**: Independent scaling for each block

### Score Calculation
- **Standard PLS**: Single score matrix T
- **MultiBlock PLS**: 
  - Block scores `t_k` for each block
  - Super scores `t_s` (weighted combination of block scores)
  - Block weights `A` and corrected weights `A_corrected`

### SPE Calculation
- **Standard PLS**: Single SPE value
- **MultiBlock PLS**: 
  - SPE for each block separately
  - Global SPE across all blocks

## Usage Examples

### Basic Usage

```python
from LVM.pls import MultiBlockPLS
import numpy as np

# Generate sample data
X1 = np.random.rand(100, 15)  # Block 1: 15 features
X2 = np.random.rand(100, 10)  # Block 2: 10 features  
X3 = np.random.rand(100, 8)   # Block 3: 8 features
Y = np.random.rand(100, 2)    # Response: 2 variables

X_blocks = [X1, X2, X3]

# Create and fit model
mbpls = MultiBlockPLS()
mbpls.fit(X_blocks, Y, n_component=5, alpha=0.95, to_be_scaled=True)

# Make predictions
X_test_blocks = [X1_test, X2_test, X3_test]
Y_pred = mbpls.predict(X_test_blocks)

# Evaluate model
eval_result = mbpls.evaluation(X_test_blocks)
```

### Accessing Multi-Block Specific Results

```python
# Block information
print(f"Number of blocks: {mbpls.n_blocks}")
print(f"Block sizes: {mbpls.block_sizes}")

# Block weights (importance of each block for each component)
print(f"Block weights (A): {mbpls.block_weights}")
print(f"Corrected block weights (A_corrected): {mbpls.block_weights_corrected}")

# Block-specific matrices
for i in range(mbpls.n_blocks):
    print(f"Block {i+1} weights (W): {mbpls.W_blocks[i].shape}")
    print(f"Block {i+1} loadings (P): {mbpls.P_blocks[i].shape}")
    print(f"Block {i+1} scores (T): {mbpls.T_blocks[i].shape}")
```

### Evaluation Results

```python
eval_result = mbpls.evaluation(X_test_blocks)

# Standard results (compatible with PlsClass)
y_predicted = eval_result.yfit
super_scores = eval_result.tscore  # t_s
hotelling_t2 = eval_result.HT2

# Multi-block specific results
block_scores = eval_result.t_blocks  # [t_k1, t_k2, t_k3]
block_spes = eval_result.spe_blocks  # SPE for each block
global_spe = eval_result.spe_global  # SPE across all blocks
reconstructed_blocks = eval_result.xhat_blocks  # Reconstructed X for each block

# Block SPE limits
spe_limits = mbpls.calculate_block_spe_limits(alpha=0.95)
```

### Monitoring and Diagnostics

```python
# Check if observations are within control limits
for i, (spe_block, spe_limit) in enumerate(zip(eval_result.spe_blocks, spe_limits)):
    outliers_block = spe_block > spe_limit
    print(f"Block {i+1} outliers: {np.sum(outliers_block)} observations")

# Global monitoring
global_outliers = eval_result.HT2 > mbpls.T2_lim[-1]
print(f"T2 outliers: {np.sum(global_outliers)} observations")

# Block contribution analysis
for i, t_k in enumerate(eval_result.t_blocks):
    contribution = mbpls.block_weights_corrected[i, :] * t_k
    print(f"Block {i+1} contribution to super scores: {contribution.shape}")
```

## Method Compatibility

All standard PLS methods are inherited and work with the concatenated representation:

```python
# These methods work the same as in standard PLS
mbpls.predict(X_blocks_test)  # Returns Y predictions
mbpls.evaluation(X_blocks_test)  # Returns mbplseval object

# Standard PLS attributes are available
mbpls.T  # Super scores (same as tscore in evaluation)
mbpls.Q  # Y loadings
mbpls.Wstar  # Global rotation matrix (same as R_global)
mbpls.B_pls  # Global regression coefficients (same as beta_global)
```

## Key Attributes

### Multi-Block Specific
- `X_blocks`: Original X data blocks
- `n_blocks`: Number of blocks
- `block_sizes`: List of block sizes
- `block_weights`: A matrix (block importances)
- `block_weights_corrected`: A_corrected matrix
- `W_blocks`: List of weight matrices for each block
- `P_blocks`: List of loading matrices for each block
- `T_blocks`: List of score matrices for each block
- `block_scaling`: Scaling parameters for each block
- `R_global`: Global rotation matrix
- `beta_global`: Global regression coefficients

### Inherited from PlsClass
- `T`: Super scores matrix
- `Q`: Y loadings matrix
- `Wstar`: Global rotation matrix (alias for R_global)
- `B_pls`: Global regression coefficients (alias for beta_global)
- `SPE_x`, `SPE_lim_x`: Global SPE statistics
- `tsquared`, `T2_lim`: Hotelling T2 statistics

## Mathematical Background

The multi-block PLS algorithm follows these key steps:

1. **Block Scaling**: Each block is scaled independently
2. **NIPALS Algorithm**: Modified for multi-block structure
   - Calculate block weights `w_k` for each block
   - Calculate block scores `t_k = X_k * w_k`
   - Calculate block importances `A_ik`
   - Calculate super scores `t_s = Σ(A_ik * t_k)`
3. **Block Weight Correction**: `A_corrected_ik = A_ik * (1 - p_i/p)`
4. **Global Matrices**: Calculate R and β for prediction

## Tips and Best Practices

1. **Block Selection**: Group related variables into blocks (e.g., spectral regions, process variables)
2. **Scaling**: Always use scaling (`to_be_scaled=True`) for multi-block data
3. **Component Selection**: Start with fewer components and increase as needed
4. **Block Monitoring**: Monitor each block separately for better fault diagnosis
5. **Compatibility**: Use the same interface as standard PLS for easy integration

## Troubleshooting

- **Import Error**: Make sure the typing module is available
- **Dimension Mismatch**: Ensure all blocks have the same number of samples
- **Scaling Issues**: Check for constant columns in any block
- **Memory Issues**: For large datasets, consider reducing the number of components

## Example Integration with Existing Code

```python
# Easy migration from standard PLS
# Old code:
# pls = PlsClass()
# pls.fit(X_concatenated, Y)

# New code:
mbpls = MultiBlockPLS()
mbpls.fit(X_blocks, Y)  # X_blocks = [X1, X2, X3] instead of concatenated X

# Same prediction interface
Y_pred = mbpls.predict(X_blocks_test)  # Instead of X_test_concatenated
```
