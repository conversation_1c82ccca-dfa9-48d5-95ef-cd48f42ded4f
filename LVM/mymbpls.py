"""
This is a custom implementation of mbpls that uses the mbpls
Model attributes after fitting
 |  ------------------------------
 |  
 |  **X-side** 
 |  
 |  Ts_ : array, super scores :math:`[n,k]`
 |  
 |  T_ : list, block scores :math:`[i][n,k]`
 |  
 |  W_ : list, block weights :math:`[i][p_i,k]`
 |  
 |  A_ : array, block importances/super weights :math:`[i,k]`
 |  
 |  A_corrected_ : array, normalized block importances :math:`A_{corr,ik} = A_{ik} \cdot (1- \frac{p_i}{p})`
 |  
 |  P_ : list, block loadings :math:`[i][p_i,k]`
 |  
 |  R_ : array, x_rotations :math:`R = W (P^T W)^{-1}`
 |  
 |  explained_var_x_ : list, explained variance in :math:`X` per LV :math:`[k]`
 |  
 |  explained_var_xblocks_ : array, explained variance in each block :math:`X_i` :math:`[i,k]`
 |  
 |  beta_ : array, regression vector :math:`\beta`  :math:`[p,q]`
 |  
 |  
 |  **Y-side**
 |  
 |  U_ : array, scoresInitialize :math:`[n,k]`
 |  
 |  V_ : array, loadings :math:`[q,k]`
 |  
 |  explained_var_y_ : list, explained variance in :math:`Y` :math:`[k]`
 |  
"""
# from mbpls.mbpls import MBPLS
import numpy as np


'''
My final choice:
- I am going to use the mbpls but overide my own methods;
for now:
1- I keep the method to be NIPLAS
2- I keep standardization to be false
3- I use my own normalizing method and normalize everything based on the global mean and std
4- I use my own way for calculating tk and then ts and then SPE and T2 and Y
5- I use direct prediction of mbpls and also transoforming using R
'''


class MyMBPLS:
    def __init__(self, n_components: int, X_org: list[np.ndarray], Y_org: np.ndarray):
        Cx = [x.mean(axis=0) for x in X_org]
        Sx = [x.std(axis=0, ddof=1) for x in X_org]
        Cy, Sy = Y_org.mean(axis=0), Y_org.std(axis=0, ddof=1)
        self.scaling_info = Cx, Sx, Cy, Sy

        X, Y = self.scaler(X_org, Y_org)
        self.X_org = X_org
        self.Y_org = Y_org
        self.X = X
        self.Y = Y
        self.n_components = n_components

        self.model: MBPLS = MBPLS(n_components=n_components, method="NIPALS", standardize=False)
        self.model.fit(X, Y)

    def predict(self, X_blocks):

        X_blocks_scaled, __ = self.scaler(X_blocks=X_blocks, Y=None)
        y_pred_scaled = self.model.predict(X_blocks_scaled)
        __, y_pred = self.unscaler(X_blocks=None, Y=y_pred_scaled)
        return y_pred

    def evaluate(self, X_blocks):
        ''' t_s is calculated for the block and then corresponding Hoteling T2 and spe is also calculated. 
        For SPE one global one accross all blocks and one for each block is calculated'''
        X_blocks_scaled, __ = self.scaler(X_blocks=X_blocks, Y=None)
        T = self.model.Ts_
        # use pk and wk to calculate tk and then use A to calculate ts and then calculate SPE and Hotelling T2 and Y_pre

        t_k = [x @ w for x, w in zip(X_blocks_scaled, self.model.W_)]
        ts = np.zeros_like(t_k[0])
        for i in range(self.model.A_.shape[0]):
            ts += self.model.A_[i, :]*t_k[i]

        Hotelin_T2 = np.sum(
            (ts/np.std(T, axis=0, ddof=1))**2, axis=1)

        xhat_ = [tk @ p.T for tk, p in zip(t_k, self.model.P_)]
        SPE_ = [np.sum((x-xhat)**2, axis=1) for x, xhat in zip(X_blocks_scaled, xhat_)]
        Xs = np.concatenate(X_blocks_scaled, axis=1)
        xhat_s = np.concatenate(xhat_, axis=1)
        SPE_s = np.sum((Xs-xhat_s)**2, axis=1)

        y_pre_scaled = <EMAIL>.V_.T
        __, y_pre = self.unscaler(X_blocks=None, Y=y_pre_scaled)

        # use R to calculate T_score and then SPE and Hotelling T2 and Y_pre

        Wstar = self.model.R_
        X_s = np.concatenate(X_blocks_scaled, axis=1)
        T_score = X_s @ Wstar
        y_pre_scaled2 = X_s @ self.model.beta_
        __, Y_pre2 = self.unscaler(X_blocks=None, Y=y_pre_scaled2)

        Y_predirect = self.model.predict(X_blocks_scaled)
        # my questin for tomorrow:
        # what is A_correct_ and how it is different from A_?
        # which one is trusted
        print(f'ts of my method = {ts}, and ts of mbpls = {T_score} ')
        print(f'y_pre of my method = {y_pre}, and Y_pre of mbpls = {Y_pre2}, direct prediction = {Y_predirect} ')

    def scaler(self, X_blocks: list[np.ndarray] = None, Y: np.ndarray = None):
        Cx, Sx, Cy, Sy = self.scaling_info
        X_blocks_scaled = [(x-Cx[i])/Sx[i] for i, x in enumerate(X_blocks)] if X_blocks is not None else 0
        Y_scaled = (Y-Cy)/Sy if Y is not None else 0
        return X_blocks_scaled, Y_scaled

    def unscaler(self, X_blocks, Y):
        Cx, Sx, Cy, Sy = self.scaling_info
        X_blocks_unscaled = [(x*Cx[i])+Sx[i] for i, x in enumerate(X_blocks)] if X_blocks is not None else 0
        Y_unscaled = (Y*Sy)+Cy if Y is not None else 0
        return X_blocks_unscaled, Y_unscaled
