# Here CPLS in a very simple manner is to be developed
import numpy as np
from LVM.pls import PlsClass as pls, plseval
from LVM.pca import PcaClass as pca, pcaeval
from spca.spca import spca
from src.Utility.dist_metric import rowwise_normalized_mae
from sklearn.cluster import KMeans
from matplotlib import pyplot as plt
# FIXME: for now I need to put lib folder and add pls and pca for github but for my own usage I would take one to LVM and revise the importing
# FIXME: in practice it only means if we use X clustering not Y

# TODO: General to do list for this:
# 1- First step: just apply clustering on X with the number of cluster specified
#   - develop a method to find the hosting cluster for a new sample (based on pca's metric)
# 2- Second step: step one but the number of cluster can be determined automatically (using pca or spca)
# 3- Third step: consider to cluster based on Y as well (still for a new sample we only have X and we need to find the hosting cluster based on pca's metric)


class CPLS:
    def __init__(self, X, Y, n_cluster=1, based_on='X'):
        # TODO: Here for now it only does the job by clustering based on X
        self.X = X
        self.Y = Y
        self.n_cluster = n_cluster
        # the block that gets clustered, for now it is X but it can be changed to Y or even a combination of X and Y
        if based_on == 'X':
            self.C = X
        elif based_on == 'Y':
            self.C = Y
        else:
            raise ValueError('based_on must be either X or Y')
        self.pca_model = pca().fit(self.C)
        self.C = self.pca_model.T

    def fit(self):
        # TODO: apply the clustering based on X and then develop a pls model for each cluster
        self._apply_kmeans()
        labels = self.kmeans.labels_
        pls_models = np.empty((self.n_cluster,), dtype=pls)
        for i in range(self.n_cluster):
            X_cluster = self.X[labels == i, :].reshape(-1, self.X.shape[1])
            Y_cluster = self.Y[labels == i, :].reshape(-1, self.Y.shape[1])
            pls_model = pls().fit(X_cluster, Y_cluster)
            pls_models[i] = pls_model
        self.pls_models = pls_models

    def predict(self, new_data):

        pls_models = self.pls_models
        predicted = np.empty((new_data.shape[0], self.Y.shape[1]))
        for i in range(new_data.shape[0]):
            hosting_cluster = self.hosting_cluster(new_data[i, :].reshape(1, -1))
            predicted[i, :] = pls_models[hosting_cluster][0].predict(new_data[i, :].reshape(1, -1))
        return predicted

    def hosting_cluster(self, new_data):
        new_data_score = self.pca_model.evaluation(new_data).tscore
        hosting_cluster = self.kmeans.predict(new_data_score)
        return hosting_cluster

    def evaluate(self, X_new, Y_new):
        Y_pre = self.predict(X_new)
        row_based_error, global_error = rowwise_normalized_mae(predicted=Y_pre, observed=Y_new, Y_org=self.Y)
        print(f'Error (AVERAGE={global_error})')
        return row_based_error, global_error

    def _apply_kmeans(self):
        # TODO: using the sklearn.cluster.KMeans apply the clustering
        n_clusters = self.n_cluster
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        kmeans.fit(self.C)
        self.kmeans: KMeans = kmeans

    def _n_cluster_determination(self, method='pca'):
        # FIXME: this is not being used
        # TODO: using the pca or spca, or elbow method(?) to determine the number of cluster
        C = self.C
        if method == 'pca':
            pca_model = pca()
            pca_model.fit(C)
            self.n_cluster = pca_model.n_component
        elif method == 'spca':
            __, coveredR2 = spca(C, plotting=False)
            self.n_cluster = 1 + np.where(np.cumsum(coveredR2) > 0.85)[0][0]
        elif method == 'elbow':
            pass
        pass
