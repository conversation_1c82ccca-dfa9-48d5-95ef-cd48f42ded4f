"""
Test script for MultiBlockPLS implementation
"""

import numpy as np
import sys
import os

# Add the parent directory to the path to import LVM modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from LVM.pls import MultiBlockPLS, mbplseval
import matplotlib.pyplot as plt


def generate_test_data():
    """Generate synthetic multi-block data for testing"""
    np.random.seed(42)
    
    num_samples = 100
    num_features_block1 = 15
    num_features_block2 = 10
    num_features_block3 = 8
    num_y_vars = 2
    
    # Generate correlated blocks
    # Block 1: Process variables
    X1 = np.random.randn(num_samples, num_features_block1)
    
    # Block 2: Spectral data (correlated with block 1)
    X2 = np.random.randn(num_samples, num_features_block2) + 0.5 * X1[:, :num_features_block2]
    
    # Block 3: Environmental variables
    X3 = np.random.randn(num_samples, num_features_block3)
    
    # Generate Y that depends on all blocks with different weights
    Y = (0.6 * np.mean(X1[:, :5], axis=1, keepdims=True) + 
         0.3 * np.mean(X2[:, :3], axis=1, keepdims=True) + 
         0.1 * np.mean(X3[:, :2], axis=1, keepdims=True) + 
         0.1 * np.random.randn(num_samples, 1))
    
    # Add second Y variable
    Y2 = (0.4 * np.mean(X1[:, 5:10], axis=1, keepdims=True) + 
          0.4 * np.mean(X2[:, 3:6], axis=1, keepdims=True) + 
          0.2 * np.mean(X3[:, 2:4], axis=1, keepdims=True) + 
          0.1 * np.random.randn(num_samples, 1))
    
    Y = np.hstack([Y, Y2])
    
    return [X1, X2, X3], Y


def test_multiblock_pls():
    """Test the MultiBlockPLS implementation"""
    print("Testing MultiBlockPLS Implementation")
    print("=" * 50)
    
    # Generate test data
    X_blocks, Y = generate_test_data()
    print(f"Generated data:")
    print(f"  Block 1 shape: {X_blocks[0].shape}")
    print(f"  Block 2 shape: {X_blocks[1].shape}")
    print(f"  Block 3 shape: {X_blocks[2].shape}")
    print(f"  Y shape: {Y.shape}")
    
    # Split data into training and testing
    train_size = 70
    X_blocks_train = [block[:train_size] for block in X_blocks]
    X_blocks_test = [block[train_size:] for block in X_blocks]
    Y_train = Y[:train_size]
    Y_test = Y[train_size:]
    
    print(f"\nTraining samples: {train_size}")
    print(f"Testing samples: {Y_test.shape[0]}")
    
    # Create and fit MultiBlockPLS model
    print("\nFitting MultiBlockPLS model...")
    mbpls = MultiBlockPLS()
    
    try:
        mbpls.fit(X_blocks_train, Y_train, n_component=5, alpha=0.95, to_be_scaled=True)
        print("✓ Model fitted successfully!")
        
        # Print model information
        print(f"\nModel Information:")
        print(f"  Number of components: {mbpls.n_component}")
        print(f"  Number of blocks: {mbpls.n_blocks}")
        print(f"  Block sizes: {mbpls.block_sizes}")
        print(f"  Block weights shape: {mbpls.block_weights.shape}")
        print(f"  Block weights corrected shape: {mbpls.block_weights_corrected.shape}")
        
        # Test prediction
        print("\nTesting prediction...")
        Y_pred = mbpls.predict(X_blocks_test)
        print(f"✓ Prediction successful! Shape: {Y_pred.shape}")
        
        # Calculate prediction error
        mse = np.mean((Y_test - Y_pred)**2)
        rmse = np.sqrt(mse)
        print(f"  RMSE: {rmse:.4f}")
        
        # Test evaluation
        print("\nTesting evaluation...")
        eval_result: mbplseval = mbpls.evaluation(X_blocks_test)
        print(f"✓ Evaluation successful!")
        print(f"  Super scores shape: {eval_result.tscore.shape}")
        print(f"  Number of block scores: {len(eval_result.t_blocks)}")
        print(f"  Number of block SPEs: {len(eval_result.spe_blocks)}")
        print(f"  Global SPE shape: {eval_result.spe_global.shape}")
        print(f"  Hotelling T2 shape: {eval_result.HT2.shape}")
        
        # Test block SPE limits
        print("\nCalculating block SPE limits...")
        spe_limits = mbpls.calculate_block_spe_limits(alpha=0.95)
        print(f"✓ Block SPE limits calculated: {len(spe_limits)} limits")
        
        # Print block weights
        print(f"\nBlock Weights (A matrix):")
        for i in range(mbpls.n_blocks):
            print(f"  Block {i+1}: {mbpls.block_weights[i, :3]}")  # First 3 components
        
        print(f"\nBlock Weights Corrected (A_corrected matrix):")
        for i in range(mbpls.n_blocks):
            print(f"  Block {i+1}: {mbpls.block_weights_corrected[i, :3]}")  # First 3 components
        
        return mbpls, eval_result
        
    except Exception as e:
        print(f"✗ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None


def compare_with_standard_pls():
    """Compare MultiBlockPLS with standard PLS on concatenated data"""
    print("\n" + "=" * 50)
    print("Comparing with Standard PLS")
    print("=" * 50)
    
    # Generate test data
    X_blocks, Y = generate_test_data()
    
    # Split data
    train_size = 70
    X_blocks_train = [block[:train_size] for block in X_blocks]
    X_blocks_test = [block[train_size:] for block in X_blocks]
    Y_train = Y[:train_size]
    Y_test = Y[train_size:]
    
    # Concatenate blocks for standard PLS
    X_concat_train = np.concatenate(X_blocks_train, axis=1)
    X_concat_test = np.concatenate(X_blocks_test, axis=1)
    
    try:
        # Standard PLS
        from LVM.pls import PlsClass
        pls_standard = PlsClass()
        pls_standard.fit(X_concat_train, Y_train, n_component=5, to_be_scaled=True)
        Y_pred_standard = pls_standard.predict(X_concat_test)
        rmse_standard = np.sqrt(np.mean((Y_test - Y_pred_standard)**2))
        
        # MultiBlock PLS
        mbpls = MultiBlockPLS()
        mbpls.fit(X_blocks_train, Y_train, n_component=5, to_be_scaled=True)
        Y_pred_mb = mbpls.predict(X_blocks_test)
        rmse_mb = np.sqrt(np.mean((Y_test - Y_pred_mb)**2))
        
        print(f"Standard PLS RMSE: {rmse_standard:.4f}")
        print(f"MultiBlock PLS RMSE: {rmse_mb:.4f}")
        print(f"Difference: {abs(rmse_standard - rmse_mb):.4f}")
        
        return rmse_standard, rmse_mb
        
    except Exception as e:
        print(f"✗ Error during comparison: {str(e)}")
        return None, None


if __name__ == "__main__":
    # Run tests
    mbpls_model, eval_result = test_multiblock_pls()
    
    if mbpls_model is not None:
        # Compare with standard PLS
        compare_with_standard_pls()
        
        print("\n" + "=" * 50)
        print("All tests completed successfully!")
        print("=" * 50)
        
        print("\nUsage Example:")
        print("```python")
        print("from LVM.pls import MultiBlockPLS")
        print("")
        print("# Create model")
        print("mbpls = MultiBlockPLS()")
        print("")
        print("# Fit model")
        print("mbpls.fit(X_blocks_train, Y_train, n_component=5)")
        print("")
        print("# Predict")
        print("Y_pred = mbpls.predict(X_blocks_test)")
        print("")
        print("# Evaluate")
        print("eval_result = mbpls.evaluation(X_blocks_test)")
        print("```")
    else:
        print("\n✗ Tests failed!")
