{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"/Users/<USER>/Documents/My_Workspace/src\")\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from LVMI.base_problem.base_case import Problem"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TDS=[[0.25663983 1.83710063 0.75800559]] and y_des is [[ 5.16960689 -1.87061256]]\n", "Itr:1,best:-0.32, bestbefore:0.70,Direct Inversion\n", "Itr:1,best:0.60, bestbefore:0.70,New Direct Inversion\n", "Itr:1,best:0.67, bestbefore:0.70,IbO\n", "Itr:1,best:0.92, bestbefore:0.70,Prep\n", "Itr:1,best:0.83, bestbefore:0.70,PDS no prep\n", "Itr:1,best:0.91, bestbefore:0.70,PDS with prep\n"]}, {"data": {"image/png": "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***********************************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", "text/plain": ["<Figure size 1200x1200 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Itr:2,best:0.62, bestbefore:0.70,Direct Inversion\n", "Itr:2,best:0.60, bestbefore:0.70,New Direct Inversion\n", "Itr:2,best:0.67, bestbefore:0.70,IbO\n", "Itr:2,best:0.92, bestbefore:0.70,Prep\n", "Itr:2,best:0.83, bestbefore:0.70,PDS no prep\n", "Itr:2,best:0.91, bestbefore:0.70,PDS with prep\n"]}, {"data": {"text/plain": ["array([[0.95070663, 0.93784152, 0.7188803 ]])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "from Projects.simulated_data.sim_data import SimData\n", "active_case = SimData(N=30, case_id=1,xvar=3,yvar=2)\n", "active_case.all_inversion(ploting=True)\n", "active_case.apply_direct()\n", "active_case.apply_new_direct()\n", "active_case.apply_ibo()\n", "active_case.apply_prep()\n", "active_case.apply_pds_ranking_dis_no_prep()\n", "active_case.apply_pds_ranking_dis_with_prep()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from inversion_assessment.method_comparison import InversionAssessment\n", "from case_studies.sim_data import SimData\n", "active_case = SimData(N=30, case_id=1, xvar=3, yvar=2)\n", "# Create assessment instance\n", "assessment = InversionAssessment(active_case)\n", "\n", "# Define methods to compare\n", "methods = {\n", "    'direct': lambda: active_case.apply_direct(ploting=False),\n", "    'direct_newMI': lambda: active_case.apply_new_direct(ploting=False),\n", "    'ibo': lambda: active_case.apply_ibo(ploting=False),\n", "    'prep': lambda: active_case.apply_prep(ploting=False),\n", "    # 'pds_no_prep': lambda: active_case.apply_pds_ranking_dis_no_prep(ploting=False),\n", "    # 'pds_with_prep': lambda: active_case.apply_pds_ranking_dis_with_prep(ploting=False),\n", "}\n", "\n", "# Run comparison\n", "summary, detailed_results = assessment.compare_methods(\n", "    methods=methods,\n", "    n_trials=10,\n", "    n_iterations=1,\n", "    accuracy_threshold=0.95\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_case.apply_direct()\n", "active_case.apply_new_direct()\n", "active_case.apply_ibo()\n", "active_case.apply_prep()\n", "active_case.apply_pds_ranking_dis_no_prep()\n", "active_case.apply_pds_ranking_dis_with_prep()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from LVM_inversions.base_problem.base_case import Problem"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from Projects.simulated_data.sim_data import SimData\n", "active_case = SimData(N=30, case_id=1,xvar=3,yvar=4)\n", "active_case.target_alignment_assessment(y_des=active_case.Y_des,knn=5)\n", "x_itr_all, y_itr_all=None,None\n", "x_itr_all, y_itr_all= active_case.all_inversion(x_itr=x_itr_all,y_itr=y_itr_all,ploting=True)\n", "\n", "# active_case.analyze_complexity(target=active_case.Y_des)\n", "\n", "# x_itr_ibo, y_itr_ibo=None,None\n", "# x_itr_org, y_itr_org=None,None\n", "# x_itr_smart, y_itr_smart=None,None\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x_itr_all, y_itr_all= active_case.all_inversion(x_itr=x_itr_all,y_itr=y_itr_all,ploting=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_case.prep_model.itre_history[-1].show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''apply all inversion'''\n", "x_itr_all, y_itr_all= active_case.all_inversion(x_itr=x_itr_all,y_itr=y_itr_all,ploting=True)\n", "\n", "''' monitor the target alignment variation'''\n", "active_case.target_alignment_assessment(y_des=active_case.Y_des,knn=5,x_itr=x_itr_all,y_itr=y_itr_all)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["''' apply prep with validations'''\n", "active_case.apply_prep(validation_inclusion=True)\n", "print(active_case.prep_model)\n", "prep_model=active_case.prep_model\n", "active_case.iteration_assess_plot(y_tried=prep_model.y_itr,y_train=prep_model.itr_history[0].Y_train)\n", "active_case.output_space_plot(active_case.Y,y_itr=active_case.prep_model.y_itr,y_des=active_case.Y_des)\n", "\n", "''' monitor the target alignment variation'''\n", "x_itr0=active_case.prep_model.itr_history[-1].xitr_\n", "y_itr0=active_case.prep_model.itr_history[-1].yitr_\n", "active_case.target_alignment_assessment(y_des=active_case.Y_des,knn=5,x_itr=x_itr0,y_itr=y_itr0)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["''' apply prep with validations'''\n", "active_case.apply_prep(validation_inclusion=False)\n", "print(active_case.prep_model)\n", "prep_model=active_case.prep_model\n", "active_case.iteration_assess_plot(y_tried=prep_model.y_itr,y_train=prep_model.itr_history[0].Y_train)\n", "active_case.output_space_plot(active_case.Y,y_itr=active_case.prep_model.y_itr,y_des=active_case.Y_des)\n", "\n", "''' monitor the target alignment variation'''\n", "x_itr0=active_case.prep_model.itr_history[-1].xitr_\n", "y_itr0=active_case.prep_model.itr_history[-1].yitr_\n", "active_case.target_alignment_assessment(y_des=active_case.Y_des,knn=5,x_itr=x_itr0,y_itr=y_itr0)\n", "active_case.iteration_assess_plot(y_tried=active_case.prep_model.y_itr,y_train=active_case.prep_model.itr_history[0].Y_train)\n", "active_case.output_space_plot(active_case.Y,y_itr=active_case.prep_model.y_itr,y_des=active_case.Y_des)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["''''apply smart inversion'''\n", "x_itr_smart, y_itr_smart=active_case.smart_inversion(x_itr=x_itr_smart,y_itr=y_itr_smart,ploting=True)\n", "\n", "''' monitor the target alignment variation'''\n", "active_case.target_alignment_assessment(y_des=active_case.Y_des,knn=5,x_itr=x_itr_smart,y_itr=y_itr_smart)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["''' apply direct inversion'''\n", "x_itr_org, y_itr_org= active_case.apply_Ibo_or_direct(x_itr=x_itr_org,y_itr=y_itr_org,mode='direct',N_itr=5,ploting=True)\n", "\n", "''' monitor the target alignment variation'''\n", "active_case.target_alignment_assessment(y_des=active_case.Y_des,knn=5,x_itr=x_itr_org,y_itr=y_itr_org)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''apply ibo inversion'''\n", "x_itr_ibo, y_itr_ibo= active_case.apply_Ibo_or_direct(x_itr=x_itr_ibo,y_itr=y_itr_ibo,mode='ibo',N_itr=5,ploting=True)\n", "\n", "''' monitor the target alignment variation'''\n", "active_case.target_alignment_assessment(y_des=active_case.Y_des,knn=5,x_itr=x_itr_ibo,y_itr=y_itr_ibo)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from case_studies.sim_data import SimData\n", "active_case = SimData(N=30, case_id=3,xvar=4,yvar=3)\n", "\n", "summary, detailed_results = active_case.compare_inversion_methods(n_trials=10, n_iterations=5)\n", "\n", "# Print best method for each trial\n", "for trial in detailed_results:\n", "    print(f\"\\nTrial {trial['trial_id']}\")\n", "    # print(f\"Difficulty (HT2 ratio): {trial['difficulty_metrics']['ht2_ratio']:.3f}\")\n", "    \n", "    # Find best method\n", "    best_method = None\n", "    best_accuracy = -float('inf')\n", "    for method, results in trial['methods'].items():\n", "        if 'accuracy' in results and results['accuracy'] > best_accuracy:\n", "            best_accuracy = results['accuracy']\n", "            best_method = method\n", "    \n", "    print(f\"Best method: {best_method} (accuracy: {best_accuracy:.3f})\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.arange(1,6)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from case_studies.sim_data import SimData\n", "active_case = SimData(N=30, case_id=3,xvar=6,yvar=3)\n", "active_case.smart_inversion(ploting=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Directly from active case\n", "# import numpy as np\n", "# from pprint import pprint\n", "# from case_studies.sim_data import SimData\n", "# active_case = SimData(N=30, case_id=1,xvar=3,yvar=2)\n", "\n", "# active_case.Y_des = SimData(N=1, case_id=1).Y\n", "x_itr_ibo, y_itr_ibo=None,None\n", "x_itr_org, y_itr_org=None,None\n", "x_itr_smart, y_itr_smart=None,None\n", "x_itr_all, y_itr_all=None,None\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_case.apply_prep(validation_inclusion=False)\n", "active_case.iteration_assess_plot(y_tried=active_case.prep_model.y_itr,y_train=active_case.prep_model.itr_history[0].Y_train)\n", "active_case.output_space_plot(active_case.Y,y_itr=active_case.prep_model.y_itr,y_des=active_case.Y_des)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_case.prep_model=None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_case.apply_prep()\n", "print(active_case.prep_model)\n", "prep_model=active_case.prep_model\n", "active_case.iteration_assess_plot(y_tried=prep_model.y_itr,y_train=prep_model.itr_history[0].Y_train)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prep_model=active_case.prep_model\n", "current_itr=prep_model.itr_history[-1]\n", "active_case.y_plot(Y_train=current_itr.Y_train,y_itr=prep_model.y_itr,y_des=prep_model.Y_des)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_case.assess_plot(y_train=active_case.prep_model.itr_history[0].Y_train,y_tried=active_case.prep_model.y_itr)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}