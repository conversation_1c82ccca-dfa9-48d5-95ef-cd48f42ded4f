import numpy as np
from abc import ABC, abstractmethod
import matplotlib.pyplot as plt


class SingleOptimClass(ABC):
    class OptimProblem:
        """ Needs:
                Cost function name
                Cost function Paramer (dictioanrry)
                nvar,VarMin,VarMax, RandomSolFcn(optional)

        """

        def __init__(self, CostFcn=None, CostFcnParam={},
                     nVar=1, VarMin=0, VarMax=1, RandomSolFcn=None,):
            self.CostFcn = CostFcn
            CostFcnParam['nfe'] = 0
            self.CostFcnParam = CostFcnParam
            self.nVar = nVar
            self.VarMin = VarMin if isinstance(VarMin, int) else VarMin*np.ones(nVar)
            self.VarMax = VarMax if isinstance(VarMax, int) else VarMax*np.ones(nVar)
            self.RandomSolFcn = RandomSolFcn

    class Lbest:
        def __init__(self):
            self.Position = None
            self.Cost = None
            self.Sol = None

        def __repr__(self):
            return f"Cost:{self.Cost},Position:{self.Position},Sol:{self.Sol}"

    def __init__(self, problem: OptimProblem = None, general_params=None):

        # Default general parameters
        optim_param = {'nPop': 200, 'MaxIt': 100, 'print': True, 'p_interval': 10}

        if general_params is not None:
            optim_param.update(general_params)

        # initiation
        self.itr = 0
        self.nfe = 0
        self.MaxIt = optim_param['MaxIt']
        self.nPop = optim_param['nPop']
        self.print = optim_param['print']
        self.printerval = optim_param['p_interval']

        self.CostFcn = problem.CostFcn
        self.CostFcnParam = problem.CostFcnParam
        self.VarMin = problem.VarMin
        self.VarMax = problem.VarMax
        self.nVar = problem.nVar
        self.rand_sol = problem.RandomSolFcn

        self.best = self.Lbest()
        self.gbest_record = np.empty((0, 1), dtype=float)
        self.nfe_record = np.empty((0, 1), dtype=int)

    @abstractmethod
    def itr_zero(self):
        " To be determined using the children "
        pass

    def __repr__(self):
        ''' show the Number iteration and the best cost'''
        return f"PSO: Itr:{self.itr},NFE:{self.nfe}, Best Cost:{self.best.Cost}"

    @abstractmethod
    def run(self, MaxIt=None):
        " To be determined using the children "
        pass

    def random_solution(self):
        ''' Generate the initial solution and update pop'''
        return np.random.uniform(self.VarMin, self.VarMax, size=(self.nVar))

    def plot_cost(self, semilog=False):
        '''semilog plot is available'''
        gbest = self.gbest_record
        nfe = self.nfe_record
        plt.figure('Iteration_Progress')
        plt.plot(nfe, gbest) if not semilog else plt.semilogy(nfe, gbest)
        plt.title(self)
        plt.xlabel('NFE')
        plt.ylabel('Cost')
        plt.draw()
        plt.pause(0.1)
