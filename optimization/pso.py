import numpy as np
from .optim_class import SingleOptimClass as soc


class Pso(soc):

    class particle:
        def __init__(self):
            self.Position = None
            self.Cost = None
            self.Sol = None
            self.velocity = None
            self.LocalBest = Pso.Lbest()

    def __init__(self, problem: soc.OptimProblem = None, pso_params=None, general_params=None):

        super().__init__(problem=problem, general_params=general_params)

        # pso related init
        default_pso_params = {
            'w': 0.7,
            'wdamp': .99,
            'c1': 1.5,
            'c2': 1.5
        }
        default_pso_params['VelMax'] = 0.1*(self.VarMax-self.VarMin)
        default_pso_params['VelMin'] = -0.1*(self.VarMax-self.VarMin)

        if pso_params is not None:
            default_pso_params.update(pso_params)

        self.optim_alg_params = default_pso_params

        self.itr_zero()
        self.run()

    def itr_zero(self):

        # receving parameters
        Cost_fcn = self.CostFcn
        cost_fcn_param = self.CostFcnParam
        RandomSolFcn = self.rand_sol if self.rand_sol is not None else self.random_solution

        Particle = [self.particle() for _ in range(self.nPop)]
        GBest = self.Lbest()
        GBest.Cost = np.inf
        for j in range(self.nPop):
            Particle[j].Position = RandomSolFcn()
            Particle[j].Cost, Particle[j].Sol = Cost_fcn(Particle[j].Position, cost_fcn_param)
            Particle[j].velocity = 0
            Particle[j].LocalBest.Position = Particle[j].Position
            Particle[j].LocalBest.Cost = Particle[j].Cost
            Particle[j].LocalBest.Sol = Particle[j].Sol

            if Particle[j].Cost < GBest.Cost:
                GBest.__dict__ = Particle[j].LocalBest.__dict__.copy()

        self.pop = Particle
        self.best.__dict__ = GBest.__dict__.copy()

    def run(self, MaxIt=None):

        # optim parameters
        MaxIt = self.MaxIt if MaxIt is None else MaxIt
        nPop = self.nPop
        printing = self.print
        print_interval = self.printerval

        Gbest = self.best
        gbest_record = np.empty((MaxIt, 1), dtype=float)
        nfe_record = np.empty((MaxIt, 1), dtype=int)

        # problem params
        VarSize = self.nVar
        VarMin = self.VarMin
        VarMax = self.VarMax
        Cost_fcn = self.CostFcn
        cost_fcn_param = self.CostFcnParam
        Particle = self.pop

        # algorithm params:
        w = self.optim_alg_params['w']
        wdamp = self.optim_alg_params['wdamp']
        c1 = self.optim_alg_params['c1']
        c2 = self.optim_alg_params['c2']
        VelMax = self.optim_alg_params['VelMax']
        VelMin = self.optim_alg_params['VelMin']

        for itr in range(MaxIt):
            for p in range(nPop):
                Particle[p].velocity = w*Particle[p].velocity + c1 * np.random.rand(VarSize) * (
                    Particle[p].LocalBest.Position - Particle[p].Position) + c2 * np.random.rand(VarSize) * (Gbest.Position - Particle[p].Position)

                # Apply Velocity Limit
                Particle[p].velocity = np.maximum(Particle[p].velocity, VelMin)
                Particle[p].velocity = np.minimum(Particle[p].velocity, VelMax)

                # Update Position
                Particle[p].Position = Particle[p].Position + Particle[p].velocity

                # Velocity Mirror Effect
                IsOutside = (Particle[p].Position < VarMin) | (Particle[p].Position > VarMax)
                Particle[p].velocity[IsOutside] = -Particle[p].velocity[IsOutside]

                # Apply Position Limits
                Particle[p].Position = np.maximum(Particle[p].Position, VarMin)
                Particle[p].Position = np.minimum(Particle[p].Position, VarMax)

                Particle[p].Cost, Particle[p].Sol = Cost_fcn(Particle[p].Position, cost_fcn_param)

                if Particle[p].Cost < Particle[p].LocalBest.Cost:
                    Particle[p].LocalBest.Cost = Particle[p].Cost
                    Particle[p].LocalBest.Position = Particle[p].Position
                    Particle[p].LocalBest.Sol = Particle[p].Sol

                    if Particle[p].Cost < Gbest.Cost:
                        Gbest.__dict__ = Particle[p].LocalBest.__dict__.copy()
            w = w*wdamp

            gbest_record[itr] = Gbest.Cost
            nfe_record[itr] = cost_fcn_param['nfe']
            # Printing
            print(
                f"Itr:{itr},nfe:{nfe_record[itr]},Best Cost:{gbest_record[itr]}") if printing and itr % print_interval == 0 else None

        # update self data
        self.itr += MaxIt
        self.nfe = cost_fcn_param['nfe']
        self.optim_alg_params['w'] = w
        self.gbest_record = np.vstack((self.gbest_record, gbest_record))
        self.nfe_record = np.vstack((self.nfe_record, nfe_record))
        self.pop = Particle
        self.best = Gbest
