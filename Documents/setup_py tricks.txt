if placing setup.py next to main folder that includes all modules and packages (where the addressing is rooted- like nspls)
    ‼️ packages=find_packages(where=".", exclude=["tests", "test_.*"]),  # Explicitly exclude tests
        - here where is telling the find package should start from the current folder that contains both setup.py and nspls folder
    include_package_data=True,
        - it means the subpackages should be implemented in the setup as well
    package_dir={"": "."},  # Map the root to the current directory
    
