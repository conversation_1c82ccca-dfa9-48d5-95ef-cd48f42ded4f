how to organize a folder in git repository:
    should create a .gitignore file and put unneccesary files in it to keep it clean
        how to create it
            simply tyoe this in terminal "touch .gitignore" 
            git add .gitignore
        open it in a text editor and put these examples
            Add a .gitignore file to exclude files/folders:
                dist/
                build/
                *.egg-info/
    
    For Release files: Github has a buit-in feature
        Create a release:
            Go to your GitHub repository.
            Click on "Releases" (on the right side, under the "About" section).
            Click "Draft a new release."
        Add distributable files:
            Upload your .whl or .tar.gz files from the dist/ folder.
            Add a version tag (e.g., v1.0.0) and release notes.
        Publish the release:
            Click "Publish release."
    GitHub will host the files for you, and others can download them directly from 
    the Releases page. No need to manually create a folder in your repository!