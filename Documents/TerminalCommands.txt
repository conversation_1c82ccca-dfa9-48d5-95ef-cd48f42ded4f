Here are the 10 most practical and frequently used commands for working with a MacBook terminal:

1. **`cd <directory>`** - Change directory (e.g., `cd Documents`).

2. **`ls`** - List files and folders in the current directory.

3. **`pwd`** - Print the current working directory (shows where you are).

4. **`mkdir <folder_name>`** - Create a new directory (e.g., `mkdir new_folder`).

5. **`rm <file>`** - Remove a file (e.g., `rm file.txt`).

6. **`rm -rf <folder>`** - Remove a folder and its contents (e.g., `rm -rf old_folder`).

7. **`cp <source> <destination>`** - Copy files or folders (e.g., `cp file.txt backup/`).

8. **`mv <source> <destination>`** - Move or rename files/folders (e.g., `mv file.txt new_name.txt`).

9. **`touch <file>`** - Create an empty file (e.g., `touch new_file.txt`).

10. **`open <file/folder>`** - Open a file or folder (e.g., `open .` opens the current directory in Finder).

These commands cover most basic file and directory operations.