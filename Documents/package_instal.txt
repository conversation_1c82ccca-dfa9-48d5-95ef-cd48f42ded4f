
more infor on what should be in setup.py is added at the end

package installed dynamically
    you can simply add its path to the system path so you can import it wherever you want.
        to do so:
                {import sys
                sys.path.append("/Users/<USER>/Documents/Self Development/GitHub-released/Projects/MainSource/Packages")}

install packages statically
    (path should refer to the folder that contains setup.py)
        put modules and a __init__.py in a folder next to it you need a setup.py.
            in setup.py you should put these at least {setup(
                                                            name="LVM",
                                                            version="1.1",
                                                            packages=find_packages(),)
                                                            }
    to install it use this: python3 -m pip install "/Users/<USER>/Documents/Self Development/GitHub-released/Projects/MainSource/Packages"
    to upgrate the package: python3 -m pip install --upgrade "/Users/<USER>/Documents/Self Development/GitHub-released/Projects/MainSource/Packages"

Create Distributable Packages?
    the same as installing yet here we make distributable file and then we can either
        install it : python3 -m pip install /path/to/dist/your_package-0.1-py3-none-any.whl
        share it : the same as above for installation
        post it to pypi : python3 -m twine upload dist/* (first need to create an acount)

    for a directory like this :
        mainproject/
            ├── mypackage/
            │   ├── __init__.py
            │   └── module1.py
            ├── folder1/
            │   └── module2.py
            ├── folder2/
            │   └── module3.py
            ├── setup.py
            └── README.md
        
        in setup.py we need this: 
        from setuptools import setup, find_packages

            setup(
                name="mainproject",
                version="0.1",
                packages=find_packages(include=["mypackage", "folder1", "folder2"]),  # Include all folders
                install_requires=[],  # Add dependencies here
            )
        bring the cd to the mainproject
            python3 setup.py sdist bdist_wheel 
        
        this would add the following files to the directory --> to install (python3 -m pip install null_space-0.1.1-py3-none-any.whl)
            ├── dist/   -->(should be distributed)
        │   ├── your_package-0.1.tar.gz
        │   └── your_package-0.1-py3-none-any.whl
        ├── build/  # Temporary files --> (can be deleted)
        ├── mypackage.egg-info/  # Metadata --> (can be deleted)







More info on how to generate a setupfile
project/
├── LVM/
│   ├── __init__.py
│   ├── PLS/
│   │   ├── __init__.py
│   │   └── pls_module.py
│   └── PCA/
│       ├── __init__.py
│       └── pca_module.py
├── setup.py
└── README.md


from setuptools import setup, find_packages

setup(
    name="LVM",  # Name of the package
    version="0.1",
    packages=find_packages(),  # Automatically find packages (LVM, LVM.PLS, LVM.PCA)
    package_dir={
        "": ".",  # Map root package to the current directory
    },
)

import LVM
from LVM.PLS import pls_module
from LVM.PCA import pca_module



