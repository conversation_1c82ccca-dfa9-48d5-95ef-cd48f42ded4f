✅ Jupyter uses its own kernel, not necessarily the VS Code interpreter.
✅ You must explicitly switch the kernel inside Jupyter.
✅ If your virtual environment is missing, manually add it to <PERSON><PERSON><PERSON> (ipykernel).



✅Step 1: Check Your Jupyter Kernel: To check if the environment is being used
    inside a Jupyter Notebook, run:
        import sys
        sys.executable
    This will show the exact Python interpreter path <PERSON><PERSON><PERSON> is using.

✅Step 2: Check Available Jupyter Kernels
    Run this in your terminal (not Jupyter Notebook):
        jupyter kernelspec list 
    This will list available Jupyter kernels.
        If your virtual environment’s Python is not listed:
            you need to add it

✅Step 3: Manually Add Your Virtual Environment to Jupyter
    python -m ipykernel install --user --name my_env --display-name "<PERSON> (my_env)"
     Replace my_env with your environment name.

✅Step 4: Select the Correct Kernel in Jupyter
    Open your Jupyter Notebook in VS Code.
    Click the kernel selector (top-right corner).
    Select "Python (my_env)" (or whatever name you used in Step 3).
    Restart the kernel and check again with:

