how to Create
    python -m venv <env_name>

how to activate
    open terminal
        go to the directory that has the virtual_folder then using (cd /path)
        source <env_name>/bin/activate
            this will simple change python interpreter to the virtual one 

how to install packages and record the requirements
    while a virtual is activated, all package installation goes to that virtual


how to switch for another Projects
    First open terminal and type this : deactivate
    now you are in globar environment
    now you can active the other project


how to delete a virtual environment
    open terminal
    go to the directory where the virtual is there
    rm -rf name_venv1

    or
    open terminal rm -rf full_path_to_the_folder_named_ven1(the name of virtual)



